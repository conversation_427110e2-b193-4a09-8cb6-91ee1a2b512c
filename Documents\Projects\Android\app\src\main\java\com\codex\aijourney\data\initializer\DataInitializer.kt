package com.codex.aijourney.data.initializer

import com.codex.aijourney.data.repository.LearningRepository
import com.codex.aijourney.data.repository.CapstoneRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DataInitializer @Inject constructor(
    private val learningRepository: LearningRepository,
    private val capstoneRepository: CapstoneRepository
) {

    fun initializeAppData() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                learningRepository.seedInitialData()
                capstoneRepository.initializeDefaultProject()
            } catch (e: Exception) {
                // Log error in a real app
                e.printStackTrace()
            }
        }
    }
}
