> Task :app:compileDebugKotlin
w:  `suppressKotlinVersionCompatibilityCheck` should specify the version of Kotlin for which you want the compatibility check to be disabled. For example, `suppressKotlinVersionCompatibilityCheck=1.9.20`
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:9:2 Unresolved reference: Dao
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:13:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:16:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:16:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:19:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:23:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:26:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:29:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:29:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:32:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:35:6 Unresolved reference: Delete
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:39:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:42:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:45:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:45:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:48:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/CapstoneDao.kt:51:6 Unresolved reference: Delete
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:9:2 Unresolved reference: Dao
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:13:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:16:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:19:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:19:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:22:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:22:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:25:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:29:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:32:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:35:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:38:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:38:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:41:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:41:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:44:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:47:6 Unresolved reference: Delete
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:51:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:54:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:57:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:57:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:60:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/dao/LearningDao.kt:63:6 Unresolved reference: Delete
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:5:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:6:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:12:2 Unresolved reference: Database
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:24:2 Unresolved reference: TypeConverters
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:25:32 Unresolved reference: RoomDatabase
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/CodexDatabase.kt:36:32 Unresolved reference: Room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:9:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:14:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:19:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:24:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:29:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/database/Converters.kt:34:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/initializer/DataInitializer.kt:8:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/initializer/DataInitializer.kt:9:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/initializer/DataInitializer.kt:11:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/initializer/DataInitializer.kt:12:24 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:6:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:8:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:17:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:19:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:27:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/CapstoneProject.kt:29:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:5:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:7:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:10:9 Unresolved reference: ForeignKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:14:24 Unresolved reference: ForeignKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningNode.kt:19:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningStage.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningStage.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningStage.kt:6:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/LearningStage.kt:8:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:5:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:7:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:10:9 Unresolved reference: ForeignKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:14:24 Unresolved reference: ForeignKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/model/UserNote.kt:19:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/preferences/NotificationPreferences.kt:6:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/preferences/NotificationPreferences.kt:7:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/preferences/NotificationPreferences.kt:9:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/preferences/NotificationPreferences.kt:10:32 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/CapstoneRepository.kt:8:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/CapstoneRepository.kt:9:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/CapstoneRepository.kt:11:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/CapstoneRepository.kt:12:27 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:10:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:11:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:13:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:14:27 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:332:38 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Array<out TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Array<out TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun BooleanArray.first(): Boolean defined in kotlin.collections
public inline fun BooleanArray.first(predicate: (Boolean) -> Boolean): Boolean defined in kotlin.collections
public fun ByteArray.first(): Byte defined in kotlin.collections
public inline fun ByteArray.first(predicate: (Byte) -> Boolean): Byte defined in kotlin.collections
public fun CharArray.first(): Char defined in kotlin.collections
public inline fun CharArray.first(predicate: (Char) -> Boolean): Char defined in kotlin.collections
public fun CharSequence.first(): Char defined in kotlin.text
public inline fun CharSequence.first(predicate: (Char) -> Boolean): Char defined in kotlin.text
public fun DoubleArray.first(): Double defined in kotlin.collections
public inline fun DoubleArray.first(predicate: (Double) -> Boolean): Double defined in kotlin.collections
public fun FloatArray.first(): Float defined in kotlin.collections
public inline fun FloatArray.first(predicate: (Float) -> Boolean): Float defined in kotlin.collections
public fun IntArray.first(): Int defined in kotlin.collections
public inline fun IntArray.first(predicate: (Int) -> Boolean): Int defined in kotlin.collections
public fun LongArray.first(): Long defined in kotlin.collections
public inline fun LongArray.first(predicate: (Long) -> Boolean): Long defined in kotlin.collections
public fun ShortArray.first(): Short defined in kotlin.collections
public inline fun ShortArray.first(predicate: (Short) -> Boolean): Short defined in kotlin.collections
public inline fun UByteArray.first(): UByte defined in kotlin.collections
public inline fun UByteArray.first(predicate: (UByte) -> Boolean): UByte defined in kotlin.collections
public inline fun UIntArray.first(): UInt defined in kotlin.collections
public inline fun UIntArray.first(predicate: (UInt) -> Boolean): UInt defined in kotlin.collections
public inline fun ULongArray.first(): ULong defined in kotlin.collections
public inline fun ULongArray.first(predicate: (ULong) -> Boolean): ULong defined in kotlin.collections
public inline fun UShortArray.first(): UShort defined in kotlin.collections
public inline fun UShortArray.first(predicate: (UShort) -> Boolean): UShort defined in kotlin.collections
public fun <T> Iterable<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Iterable<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun <T> List<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public fun CharProgression.first(): Char defined in kotlin.ranges
public fun IntProgression.first(): Int defined in kotlin.ranges
public fun LongProgression.first(): Long defined in kotlin.ranges
public fun UIntProgression.first(): UInt defined in kotlin.ranges
public fun ULongProgression.first(): ULong defined in kotlin.ranges
public fun <T> Sequence<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.sequences
public inline fun <T> Sequence<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.sequences
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:333:44 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:334:50 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:356:38 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Array<out TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Array<out TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun BooleanArray.first(): Boolean defined in kotlin.collections
public inline fun BooleanArray.first(predicate: (Boolean) -> Boolean): Boolean defined in kotlin.collections
public fun ByteArray.first(): Byte defined in kotlin.collections
public inline fun ByteArray.first(predicate: (Byte) -> Boolean): Byte defined in kotlin.collections
public fun CharArray.first(): Char defined in kotlin.collections
public inline fun CharArray.first(predicate: (Char) -> Boolean): Char defined in kotlin.collections
public fun CharSequence.first(): Char defined in kotlin.text
public inline fun CharSequence.first(predicate: (Char) -> Boolean): Char defined in kotlin.text
public fun DoubleArray.first(): Double defined in kotlin.collections
public inline fun DoubleArray.first(predicate: (Double) -> Boolean): Double defined in kotlin.collections
public fun FloatArray.first(): Float defined in kotlin.collections
public inline fun FloatArray.first(predicate: (Float) -> Boolean): Float defined in kotlin.collections
public fun IntArray.first(): Int defined in kotlin.collections
public inline fun IntArray.first(predicate: (Int) -> Boolean): Int defined in kotlin.collections
public fun LongArray.first(): Long defined in kotlin.collections
public inline fun LongArray.first(predicate: (Long) -> Boolean): Long defined in kotlin.collections
public fun ShortArray.first(): Short defined in kotlin.collections
public inline fun ShortArray.first(predicate: (Short) -> Boolean): Short defined in kotlin.collections
public inline fun UByteArray.first(): UByte defined in kotlin.collections
public inline fun UByteArray.first(predicate: (UByte) -> Boolean): UByte defined in kotlin.collections
public inline fun UIntArray.first(): UInt defined in kotlin.collections
public inline fun UIntArray.first(predicate: (UInt) -> Boolean): UInt defined in kotlin.collections
public inline fun ULongArray.first(): ULong defined in kotlin.collections
public inline fun ULongArray.first(predicate: (ULong) -> Boolean): ULong defined in kotlin.collections
public inline fun UShortArray.first(): UShort defined in kotlin.collections
public inline fun UShortArray.first(predicate: (UShort) -> Boolean): UShort defined in kotlin.collections
public fun <T> Iterable<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Iterable<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun <T> List<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public fun CharProgression.first(): Char defined in kotlin.ranges
public fun IntProgression.first(): Int defined in kotlin.ranges
public fun LongProgression.first(): Long defined in kotlin.ranges
public fun UIntProgression.first(): UInt defined in kotlin.ranges
public fun ULongProgression.first(): ULong defined in kotlin.ranges
public fun <T> Sequence<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.sequences
public inline fun <T> Sequence<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.sequences
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:358:27 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:361:81 Overload resolution ambiguity: 
public final operator fun minus(other: Byte): Long defined in kotlin.Long
public final operator fun minus(other: Double): Double defined in kotlin.Long
public final operator fun minus(other: Float): Float defined in kotlin.Long
public final operator fun minus(other: Int): Long defined in kotlin.Long
public final operator fun minus(other: Long): Long defined in kotlin.Long
public final operator fun minus(other: Short): Long defined in kotlin.Long
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:366:38 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Array<out TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Array<out TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun BooleanArray.first(): Boolean defined in kotlin.collections
public inline fun BooleanArray.first(predicate: (Boolean) -> Boolean): Boolean defined in kotlin.collections
public fun ByteArray.first(): Byte defined in kotlin.collections
public inline fun ByteArray.first(predicate: (Byte) -> Boolean): Byte defined in kotlin.collections
public fun CharArray.first(): Char defined in kotlin.collections
public inline fun CharArray.first(predicate: (Char) -> Boolean): Char defined in kotlin.collections
public fun CharSequence.first(): Char defined in kotlin.text
public inline fun CharSequence.first(predicate: (Char) -> Boolean): Char defined in kotlin.text
public fun DoubleArray.first(): Double defined in kotlin.collections
public inline fun DoubleArray.first(predicate: (Double) -> Boolean): Double defined in kotlin.collections
public fun FloatArray.first(): Float defined in kotlin.collections
public inline fun FloatArray.first(predicate: (Float) -> Boolean): Float defined in kotlin.collections
public fun IntArray.first(): Int defined in kotlin.collections
public inline fun IntArray.first(predicate: (Int) -> Boolean): Int defined in kotlin.collections
public fun LongArray.first(): Long defined in kotlin.collections
public inline fun LongArray.first(predicate: (Long) -> Boolean): Long defined in kotlin.collections
public fun ShortArray.first(): Short defined in kotlin.collections
public inline fun ShortArray.first(predicate: (Short) -> Boolean): Short defined in kotlin.collections
public inline fun UByteArray.first(): UByte defined in kotlin.collections
public inline fun UByteArray.first(predicate: (UByte) -> Boolean): UByte defined in kotlin.collections
public inline fun UIntArray.first(): UInt defined in kotlin.collections
public inline fun UIntArray.first(predicate: (UInt) -> Boolean): UInt defined in kotlin.collections
public inline fun ULongArray.first(): ULong defined in kotlin.collections
public inline fun ULongArray.first(predicate: (ULong) -> Boolean): ULong defined in kotlin.collections
public inline fun UShortArray.first(): UShort defined in kotlin.collections
public inline fun UShortArray.first(predicate: (UShort) -> Boolean): UShort defined in kotlin.collections
public fun <T> Iterable<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Iterable<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun <T> List<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public fun CharProgression.first(): Char defined in kotlin.ranges
public fun IntProgression.first(): Int defined in kotlin.ranges
public fun LongProgression.first(): Long defined in kotlin.ranges
public fun UIntProgression.first(): UInt defined in kotlin.ranges
public fun ULongProgression.first(): ULong defined in kotlin.ranges
public fun <T> Sequence<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.sequences
public inline fun <T> Sequence<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.sequences
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:367:37 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Array<out TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Array<out TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun BooleanArray.first(): Boolean defined in kotlin.collections
public inline fun BooleanArray.first(predicate: (Boolean) -> Boolean): Boolean defined in kotlin.collections
public fun ByteArray.first(): Byte defined in kotlin.collections
public inline fun ByteArray.first(predicate: (Byte) -> Boolean): Byte defined in kotlin.collections
public fun CharArray.first(): Char defined in kotlin.collections
public inline fun CharArray.first(predicate: (Char) -> Boolean): Char defined in kotlin.collections
public fun CharSequence.first(): Char defined in kotlin.text
public inline fun CharSequence.first(predicate: (Char) -> Boolean): Char defined in kotlin.text
public fun DoubleArray.first(): Double defined in kotlin.collections
public inline fun DoubleArray.first(predicate: (Double) -> Boolean): Double defined in kotlin.collections
public fun FloatArray.first(): Float defined in kotlin.collections
public inline fun FloatArray.first(predicate: (Float) -> Boolean): Float defined in kotlin.collections
public fun IntArray.first(): Int defined in kotlin.collections
public inline fun IntArray.first(predicate: (Int) -> Boolean): Int defined in kotlin.collections
public fun LongArray.first(): Long defined in kotlin.collections
public inline fun LongArray.first(predicate: (Long) -> Boolean): Long defined in kotlin.collections
public fun ShortArray.first(): Short defined in kotlin.collections
public inline fun ShortArray.first(predicate: (Short) -> Boolean): Short defined in kotlin.collections
public inline fun UByteArray.first(): UByte defined in kotlin.collections
public inline fun UByteArray.first(predicate: (UByte) -> Boolean): UByte defined in kotlin.collections
public inline fun UIntArray.first(): UInt defined in kotlin.collections
public inline fun UIntArray.first(predicate: (UInt) -> Boolean): UInt defined in kotlin.collections
public inline fun ULongArray.first(): ULong defined in kotlin.collections
public inline fun ULongArray.first(predicate: (ULong) -> Boolean): ULong defined in kotlin.collections
public inline fun UShortArray.first(): UShort defined in kotlin.collections
public inline fun UShortArray.first(predicate: (UShort) -> Boolean): UShort defined in kotlin.collections
public fun <T> Iterable<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Iterable<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun <T> List<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public fun CharProgression.first(): Char defined in kotlin.ranges
public fun IntProgression.first(): Int defined in kotlin.ranges
public fun LongProgression.first(): Long defined in kotlin.ranges
public fun UIntProgression.first(): UInt defined in kotlin.ranges
public fun ULongProgression.first(): ULong defined in kotlin.ranges
public fun <T> Sequence<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.sequences
public inline fun <T> Sequence<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.sequences
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:369:42 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:370:48 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:371:30 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:371:69 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:378:38 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Array<out TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Array<out TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun BooleanArray.first(): Boolean defined in kotlin.collections
public inline fun BooleanArray.first(predicate: (Boolean) -> Boolean): Boolean defined in kotlin.collections
public fun ByteArray.first(): Byte defined in kotlin.collections
public inline fun ByteArray.first(predicate: (Byte) -> Boolean): Byte defined in kotlin.collections
public fun CharArray.first(): Char defined in kotlin.collections
public inline fun CharArray.first(predicate: (Char) -> Boolean): Char defined in kotlin.collections
public fun CharSequence.first(): Char defined in kotlin.text
public inline fun CharSequence.first(predicate: (Char) -> Boolean): Char defined in kotlin.text
public fun DoubleArray.first(): Double defined in kotlin.collections
public inline fun DoubleArray.first(predicate: (Double) -> Boolean): Double defined in kotlin.collections
public fun FloatArray.first(): Float defined in kotlin.collections
public inline fun FloatArray.first(predicate: (Float) -> Boolean): Float defined in kotlin.collections
public fun IntArray.first(): Int defined in kotlin.collections
public inline fun IntArray.first(predicate: (Int) -> Boolean): Int defined in kotlin.collections
public fun LongArray.first(): Long defined in kotlin.collections
public inline fun LongArray.first(predicate: (Long) -> Boolean): Long defined in kotlin.collections
public fun ShortArray.first(): Short defined in kotlin.collections
public inline fun ShortArray.first(predicate: (Short) -> Boolean): Short defined in kotlin.collections
public inline fun UByteArray.first(): UByte defined in kotlin.collections
public inline fun UByteArray.first(predicate: (UByte) -> Boolean): UByte defined in kotlin.collections
public inline fun UIntArray.first(): UInt defined in kotlin.collections
public inline fun UIntArray.first(predicate: (UInt) -> Boolean): UInt defined in kotlin.collections
public inline fun ULongArray.first(): ULong defined in kotlin.collections
public inline fun ULongArray.first(predicate: (ULong) -> Boolean): ULong defined in kotlin.collections
public inline fun UShortArray.first(): UShort defined in kotlin.collections
public inline fun UShortArray.first(predicate: (UShort) -> Boolean): UShort defined in kotlin.collections
public fun <T> Iterable<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public inline fun <T> Iterable<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.collections
public fun <T> List<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.collections
public fun CharProgression.first(): Char defined in kotlin.ranges
public fun IntProgression.first(): Int defined in kotlin.ranges
public fun LongProgression.first(): Long defined in kotlin.ranges
public fun UIntProgression.first(): UInt defined in kotlin.ranges
public fun ULongProgression.first(): ULong defined in kotlin.ranges
public fun <T> Sequence<TypeVariable(T)>.first(): TypeVariable(T) defined in kotlin.sequences
public inline fun <T> Sequence<TypeVariable(T)>.first(predicate: (TypeVariable(T)) -> Boolean): TypeVariable(T) defined in kotlin.sequences
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/data/repository/LearningRepository.kt:379:31 Unresolved reference: it
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:13:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:14:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:15:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:16:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:17:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:18:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:20:2 Unresolved reference: Module
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:21:2 Unresolved reference: InstallIn
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:21:12 Unresolved reference: SingletonComponent
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:24:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:25:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:26:31 Unresolved reference: ApplicationContext
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:27:16 Unresolved reference: Room
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:36:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:41:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:46:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:47:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:48:36 Unresolved reference: ApplicationContext
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:52:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:53:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:54:39 Unresolved reference: ApplicationContext
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:58:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:59:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/di/DatabaseModule.kt:60:41 Unresolved reference: ApplicationContext
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:4:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:5:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:6:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:12:36 Unresolved reference: NavHostController
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:13:5 Unresolved reference: NavHost
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:17:9 Unresolved reference: composable
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:18:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:28:9 Unresolved reference: composable
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:31:13 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:33:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:42:9 Unresolved reference: composable
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/CodexNavigation.kt:43:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:3:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:4:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:5:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:9:25 Unresolved reference: NamedNavArgument
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:16:13 Unresolved reference: navArgument
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:17:17 Unresolved reference: type
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/navigation/Screen.kt:17:24 Unresolved reference: NavType
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:11:28 Unresolved reference: MainActivity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:13:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:14:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:16:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:17:27 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:75:38 Unresolved reference: MainActivity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:105:38 Unresolved reference: MainActivity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationHelper.kt:139:38 Unresolved reference: MainActivity
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:4:17 Unresolved reference: work
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:9:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:10:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:12:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:13:30 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:17:31 Unresolved reference: WorkManager
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:29:27 Unresolved reference: Constraints
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:30:37 Unresolved reference: NetworkType
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:34:32 Unresolved reference: PeriodicWorkRequestBuilder
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:43:13 Unresolved reference: ExistingPeriodicWorkPolicy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:53:27 Unresolved reference: Constraints
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:54:37 Unresolved reference: NetworkType
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:58:33 Unresolved reference: PeriodicWorkRequestBuilder
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:67:13 Unresolved reference: ExistingPeriodicWorkPolicy
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:73:27 Unresolved reference: Constraints
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:74:37 Unresolved reference: NetworkType
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:77:25 Unresolved reference: Data
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:81:32 Unresolved reference: OneTimeWorkRequestBuilder
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:91:27 Unresolved reference: Constraints
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:92:37 Unresolved reference: NetworkType
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:95:25 Unresolved reference: Data
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:99:38 Unresolved reference: OneTimeWorkRequestBuilder
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:118:33 Unresolved reference: OneTimeWorkRequestBuilder
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:211:19 Unresolved reference: WorkerParameters
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:212:5 Unresolved reference: CoroutineWorker
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:214:5 'doWork' overrides nothing
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:214:36 One type argument expected for class Result<out T>
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:216:51 Unresolved reference: applicationContext
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:218:20 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:218:27 No value passed for parameter 'value'
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:220:20 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/notifications/NotificationScheduler.kt:220:27 No value passed for parameter 'exception'
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/components/CapstoneComponents.kt:136:42 Unresolved reference: Assignment
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/components/CapstoneComponents.kt:142:42 Unresolved reference: Psychology
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/components/NodeDetailComponents.kt:110:43 Unresolved reference: OpenInNew
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/components/NodeDetailComponents.kt:125:39 Unresolved reference: Assignment
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubScreen.kt:16:17 Unresolved reference: hilt
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubScreen.kt:23:39 Unresolved reference: hiltViewModel
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubScreen.kt:56:36 Unresolved reference: Psychology
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubViewModel.kt:8:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubViewModel.kt:12:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubViewModel.kt:33:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/capstone/CapstoneHubViewModel.kt:34:29 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/detail/NodeDetailScreen.kt:21:17 Unresolved reference: hilt
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/detail/NodeDetailScreen.kt:36:38 Unresolved reference: hiltViewModel
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/detail/NodeDetailViewModel.kt:10:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/detail/NodeDetailViewModel.kt:14:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/detail/NodeDetailViewModel.kt:26:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/detail/NodeDetailViewModel.kt:27:28 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/roadmap/RoadmapScreen.kt:16:17 Unresolved reference: hilt
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/roadmap/RoadmapScreen.kt:27:35 Unresolved reference: hiltViewModel
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/roadmap/RoadmapViewModel.kt:9:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/roadmap/RoadmapViewModel.kt:12:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/roadmap/RoadmapViewModel.kt:21:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/ui/screens/roadmap/RoadmapViewModel.kt:22:25 Unresolved reference: Inject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:4:17 Unresolved reference: hilt
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:5:17 Unresolved reference: work
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:6:17 Unresolved reference: work
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:10:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:11:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:14:2 Unresolved reference: HiltWorker
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:15:31 Unresolved reference: AssistedInject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:16:6 Unresolved reference: Assisted
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:17:6 Unresolved reference: Assisted
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:17:29 Unresolved reference: WorkerParameters
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:20:5 Unresolved reference: CoroutineWorker
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:22:5 'doWork' overrides nothing
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:22:36 One type argument expected for class Result<out T>
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:91:20 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:91:27 No value passed for parameter 'value'
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:93:20 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/LearningReminderWorker.kt:93:27 No value passed for parameter 'exception'
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:4:17 Unresolved reference: hilt
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:5:17 Unresolved reference: work
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:6:17 Unresolved reference: work
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:12:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:13:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:18:2 Unresolved reference: HiltWorker
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:19:27 Unresolved reference: AssistedInject
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:20:6 Unresolved reference: Assisted
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:21:6 Unresolved reference: Assisted
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:21:29 Unresolved reference: WorkerParameters
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:25:5 Unresolved reference: CoroutineWorker
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:27:5 'doWork' overrides nothing
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:27:36 One type argument expected for class Result<out T>
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:61:20 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:61:27 No value passed for parameter 'value'
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:63:20 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/Documents/Projects/Android/app/src/main/java/com/codex/aijourney/workers/WeeklyDigestWorker.kt:63:27 No value passed for parameter 'exception'

> Task :app:compileDebugKotlin FAILED

[Incubating] Problems report is available at: file:///C:/Users/<USER>/Documents/Projects/Android/build/reports/problems/problems-report.html

