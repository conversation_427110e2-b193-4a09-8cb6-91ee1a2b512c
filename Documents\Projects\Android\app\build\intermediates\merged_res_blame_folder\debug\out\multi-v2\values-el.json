{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,405,523,603,702,819,966,1090,1244,1330,1426,1521,1622,1736,1858,1959,2097,2229,2369,2545,2679,2795,2919,3040,3136,3231,3363,3496,3598,3700,3806,3945,4094,4204,4305,4388,4467,4553,4638,4737,4813,4892,4987,5085,5178,5272,5355,5457,5552,5649,5766,5842,5944", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "170,290,400,518,598,697,814,961,1085,1239,1325,1421,1516,1617,1731,1853,1954,2092,2224,2364,2540,2674,2790,2914,3035,3131,3226,3358,3491,3593,3695,3801,3940,4089,4199,4300,4383,4462,4548,4633,4732,4808,4887,4982,5080,5173,5267,5350,5452,5547,5644,5761,5837,5939,6042"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,345,455,1493,1573,1672,1789,1936,2060,2214,2300,2396,2491,2592,2706,2828,2929,3067,3199,3339,3515,3649,3765,3889,4010,4106,4201,4333,4466,4568,4670,4776,4915,5064,5174,5481,5656,6390,6551,6737,7108,7184,7263,7358,7456,7549,7643,7726,7828,7923,8020,8137,8213,8315", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "220,340,450,568,1568,1667,1784,1931,2055,2209,2295,2391,2486,2587,2701,2823,2924,3062,3194,3334,3510,3644,3760,3884,4005,4101,4196,4328,4461,4563,4665,4771,4910,5059,5169,5270,5559,5730,6471,6631,6831,7179,7258,7353,7451,7544,7638,7721,7823,7918,8015,8132,8208,8310,8413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,671,774,874,977,1085,1191,6636", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "666,769,869,972,1080,1186,1303,6732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1012,1080,1161,1243,1318,1397,1467", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1007,1075,1156,1238,1313,1392,1462,1585"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1308,1407,5275,5376,5564,5735,5816,5910,5999,6089,6159,6227,6308,6476,6836,6915,6985", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "1402,1488,5371,5476,5651,5811,5905,5994,6084,6154,6222,6303,6385,6546,6910,6980,7103"}}]}]}