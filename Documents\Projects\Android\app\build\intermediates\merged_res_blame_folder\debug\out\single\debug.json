[{"merged": "com.codex.aijourney.app-merged_res-63:/drawable_ic_launcher_foreground.xml.flat", "source": "com.codex.aijourney.app-main-65:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.codex.aijourney.app-merged_res-63:/drawable_ic_launcher.xml.flat", "source": "com.codex.aijourney.app-main-65:/drawable/ic_launcher.xml"}, {"merged": "com.codex.aijourney.app-merged_res-63:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.codex.aijourney.app-main-65:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.codex.aijourney.app-merged_res-63:/xml_backup_rules.xml.flat", "source": "com.codex.aijourney.app-main-65:/xml/backup_rules.xml"}, {"merged": "com.codex.aijourney.app-merged_res-63:/drawable_ic_launcher_background.xml.flat", "source": "com.codex.aijourney.app-main-65:/drawable/ic_launcher_background.xml"}, {"merged": "com.codex.aijourney.app-merged_res-63:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.codex.aijourney.app-main-65:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.codex.aijourney.app-merged_res-63:/xml_data_extraction_rules.xml.flat", "source": "com.codex.aijourney.app-main-65:/xml/data_extraction_rules.xml"}]