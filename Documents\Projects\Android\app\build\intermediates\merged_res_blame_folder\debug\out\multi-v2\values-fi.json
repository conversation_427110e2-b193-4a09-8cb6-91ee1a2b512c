{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,381,491,571,668,783,916,1034,1182,1268,1360,1454,1552,1666,1785,1882,2010,2138,2263,2426,2544,2664,2788,2907,3002,3097,3214,3332,3431,3538,3642,3776,3916,4020,4121,4200,4279,4359,4441,4537,4613,4694,4787,4886,4979,5077,5163,5267,5366,5469,5583,5659,5760", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "162,271,376,486,566,663,778,911,1029,1177,1263,1355,1449,1547,1661,1780,1877,2005,2133,2258,2421,2539,2659,2783,2902,2997,3092,3209,3327,3426,3533,3637,3771,3911,4015,4116,4195,4274,4354,4436,4532,4608,4689,4782,4881,4974,5072,5158,5262,5361,5464,5578,5654,5755,5850"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,326,431,1454,1534,1631,1746,1879,1997,2145,2231,2323,2417,2515,2629,2748,2845,2973,3101,3226,3389,3507,3627,3751,3870,3965,4060,4177,4295,4394,4501,4605,4739,4879,4983,5284,5452,6171,6323,6506,6873,6949,7030,7123,7222,7315,7413,7499,7603,7702,7805,7919,7995,8096", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "212,321,426,536,1529,1626,1741,1874,1992,2140,2226,2318,2412,2510,2624,2743,2840,2968,3096,3221,3384,3502,3622,3746,3865,3960,4055,4172,4290,4389,4496,4600,4734,4874,4978,5079,5358,5526,6246,6400,6597,6944,7025,7118,7217,7310,7408,7494,7598,7697,7800,7914,7990,8091,8186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "541,637,739,837,942,1047,1159,6405", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "632,734,832,937,1042,1154,1270,6501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1275,1369,5084,5183,5363,5531,5608,5701,5792,5874,5940,6008,6089,6251,6602,6679,6751", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "1364,1449,5178,5279,5447,5603,5696,5787,5869,5935,6003,6084,6166,6318,6674,6746,6868"}}]}]}