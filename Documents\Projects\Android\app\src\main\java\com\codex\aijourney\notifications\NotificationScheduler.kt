package com.codex.aijourney.notifications

import android.content.Context
import androidx.work.*
import com.codex.aijourney.workers.LearningReminderWorker
import com.codex.aijourney.workers.WeeklyDigestWorker
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationScheduler @Inject constructor(
    private val context: Context
) {
    
    private val workManager = WorkManager.getInstance(context)
    
    fun scheduleAllNotifications() {
        scheduleLearningReminders()
        scheduleWeeklyDigest()
    }
    
    fun scheduleLearningReminders() {
        // Cancel existing work
        workManager.cancelUniqueWork(LearningReminderWorker.WORK_NAME)
        
        // Schedule daily learning reminders at 7 PM
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val dailyWorkRequest = PeriodicWorkRequestBuilder<LearningReminderWorker>(
            1, TimeUnit.DAYS
        )
            .setConstraints(constraints)
            .setInitialDelay(calculateInitialDelay(19, 0), TimeUnit.MILLISECONDS) // 7 PM
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            LearningReminderWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE,
            dailyWorkRequest
        )
    }
    
    fun scheduleWeeklyDigest() {
        // Cancel existing work
        workManager.cancelUniqueWork(WeeklyDigestWorker.WORK_NAME)
        
        // Schedule weekly digest on Sundays at 6 PM
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val weeklyWorkRequest = PeriodicWorkRequestBuilder<WeeklyDigestWorker>(
            7, TimeUnit.DAYS
        )
            .setConstraints(constraints)
            .setInitialDelay(calculateWeeklyDelay(Calendar.SUNDAY, 18, 0), TimeUnit.MILLISECONDS)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            WeeklyDigestWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE,
            weeklyWorkRequest
        )
    }
    
    fun scheduleContextualNudge(delayHours: Long, message: String) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val inputData = Data.Builder()
            .putString("message", message)
            .build()
        
        val nudgeWorkRequest = OneTimeWorkRequestBuilder<LearningReminderWorker>()
            .setConstraints(constraints)
            .setInitialDelay(delayHours, TimeUnit.HOURS)
            .setInputData(inputData)
            .build()
        
        workManager.enqueue(nudgeWorkRequest)
    }
    
    fun scheduleProgressCelebration(achievement: String, delayMinutes: Long = 5) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val inputData = Data.Builder()
            .putString("achievement", achievement)
            .build()
        
        val celebrationWorkRequest = OneTimeWorkRequestBuilder<LearningReminderWorker>()
            .setConstraints(constraints)
            .setInitialDelay(delayMinutes, TimeUnit.MINUTES)
            .setInputData(inputData)
            .build()
        
        workManager.enqueue(celebrationWorkRequest)
    }
    
    fun cancelAllNotifications() {
        workManager.cancelUniqueWork(LearningReminderWorker.WORK_NAME)
        workManager.cancelUniqueWork(WeeklyDigestWorker.WORK_NAME)
    }
    
    fun pauseNotifications(durationHours: Long) {
        // Cancel current notifications
        cancelAllNotifications()
        
        // Reschedule after the pause duration
        val resumeWorkRequest = OneTimeWorkRequestBuilder<NotificationResumeWorker>()
            .setInitialDelay(durationHours, TimeUnit.HOURS)
            .build()
        
        workManager.enqueue(resumeWorkRequest)
    }
    
    private fun calculateInitialDelay(targetHour: Int, targetMinute: Int): Long {
        val calendar = Calendar.getInstance()
        val now = calendar.timeInMillis
        
        // Set target time for today
        calendar.set(Calendar.HOUR_OF_DAY, targetHour)
        calendar.set(Calendar.MINUTE, targetMinute)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        
        var targetTime = calendar.timeInMillis
        
        // If target time has passed today, schedule for tomorrow
        if (targetTime <= now) {
            targetTime += TimeUnit.DAYS.toMillis(1)
        }
        
        return targetTime - now
    }
    
    private fun calculateWeeklyDelay(targetDayOfWeek: Int, targetHour: Int, targetMinute: Int): Long {
        val calendar = Calendar.getInstance()
        val now = calendar.timeInMillis
        
        // Set target time
        calendar.set(Calendar.DAY_OF_WEEK, targetDayOfWeek)
        calendar.set(Calendar.HOUR_OF_DAY, targetHour)
        calendar.set(Calendar.MINUTE, targetMinute)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        
        var targetTime = calendar.timeInMillis
        
        // If target time has passed this week, schedule for next week
        if (targetTime <= now) {
            targetTime += TimeUnit.DAYS.toMillis(7)
        }
        
        return targetTime - now
    }
    
    // Smart scheduling based on user behavior
    fun scheduleSmartReminders(
        hasInProgressNodes: Boolean,
        daysSinceLastActivity: Int,
        currentStage: String
    ) {
        when {
            daysSinceLastActivity == 0 -> {
                // User is active, schedule gentle reminder for tomorrow
                scheduleContextualNudge(
                    delayHours = 24,
                    message = "Great progress today! Ready to continue your $currentStage journey tomorrow?"
                )
            }
            
            daysSinceLastActivity in 1..2 -> {
                // User missed a day or two, gentle nudge
                scheduleContextualNudge(
                    delayHours = 2,
                    message = "Your AI learning journey is waiting! Just 30 minutes today can make a big difference."
                )
            }
            
            daysSinceLastActivity in 3..6 -> {
                // User has been away for a while, motivational message
                scheduleContextualNudge(
                    delayHours = 1,
                    message = "We miss you! Your $currentStage progress is still here. Ready to jump back in?"
                )
            }
            
            daysSinceLastActivity >= 7 -> {
                // User has been away for a week, re-engagement message
                scheduleContextualNudge(
                    delayHours = 0,
                    message = "Welcome back! Your AI learning journey is ready to continue. Let's recap where you left off."
                )
            }
        }
    }
}

// Worker to resume notifications after pause
class NotificationResumeWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            val scheduler = NotificationScheduler(applicationContext)
            scheduler.scheduleAllNotifications()
            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }
}
