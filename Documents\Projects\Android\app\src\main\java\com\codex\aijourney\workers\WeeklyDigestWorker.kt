package com.codex.aijourney.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.codex.aijourney.data.model.NodeStatus
import com.codex.aijourney.data.model.StageType
import com.codex.aijourney.data.repository.CapstoneRepository
import com.codex.aijourney.data.repository.LearningRepository
import com.codex.aijourney.notifications.NotificationHelper
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first
import java.util.*
import java.util.concurrent.TimeUnit

@HiltWorker
class WeeklyDigestWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val learningRepository: LearningRepository,
    private val capstoneRepository: CapstoneRepository,
    private val notificationHelper: NotificationHelper
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            val allNodes = learningRepository.getAllNodes().first()
            val stages = learningRepository.getAllStages().first()
            val projectProgress = capstoneRepository.getProjectProgress()
            
            // Calculate weekly progress
            val weekAgo = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7)
            val recentlyCompleted = allNodes.filter { node ->
                node.status == NodeStatus.COMPLETED && 
                node.completedAt != null && 
                node.completedAt!! > weekAgo
            }
            
            val totalCompleted = allNodes.count { it.status == NodeStatus.COMPLETED }
            val totalNodes = allNodes.size
            
            // Determine current stage
            val currentStage = getCurrentStage(allNodes, stages)
            
            // Generate achievements
            val achievements = generateAchievements(recentlyCompleted, projectProgress, stages)
            
            // Generate next week's focus
            val nextWeekFocus = generateNextWeekFocus(allNodes, stages, currentStage)
            
            notificationHelper.showWeeklyDigest(
                completedNodes = totalCompleted,
                totalNodes = totalNodes,
                currentStage = currentStage?.title ?: "Getting Started",
                nextWeekFocus = nextWeekFocus,
                achievements = achievements
            )
            
            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }
    
    private fun getCurrentStage(allNodes: List<com.codex.aijourney.data.model.LearningNode>, stages: List<com.codex.aijourney.data.model.LearningStage>): com.codex.aijourney.data.model.LearningStage? {
        return stages.find { stage ->
            val stageNodes = allNodes.filter { it.stageId == stage.id }
            stageNodes.any { it.status == NodeStatus.IN_PROGRESS || it.status == NodeStatus.TODO }
        }
    }
    
    private suspend fun generateAchievements(
        recentlyCompleted: List<com.codex.aijourney.data.model.LearningNode>,
        projectProgress: com.codex.aijourney.data.repository.ProjectProgress,
        stages: List<com.codex.aijourney.data.model.LearningStage>
    ): List<String> {
        val achievements = mutableListOf<String>()
        
        // Learning achievements
        when (recentlyCompleted.size) {
            in 1..2 -> achievements.add("Completed ${recentlyCompleted.size} learning module${if (recentlyCompleted.size > 1) "s" else ""}")
            in 3..4 -> achievements.add("🔥 Completed ${recentlyCompleted.size} modules - you're on fire!")
            in 5..Int.MAX_VALUE -> achievements.add("🚀 Amazing! Completed ${recentlyCompleted.size} modules this week!")
        }
        
        // Stage completion achievements
        val completedStages = stages.filter { stage ->
            val allNodes = learningRepository.getAllNodes().first()
            val stageNodes = allNodes.filter { it.stageId == stage.id }
            stageNodes.isNotEmpty() && stageNodes.all { it.status == NodeStatus.COMPLETED }
        }
        
        if (completedStages.isNotEmpty()) {
            val latestStage = completedStages.maxByOrNull { it.order }
            latestStage?.let {
                achievements.add("🎓 Completed ${it.title} stage!")
            }
        }
        
        // Project achievements
        if (projectProgress.isProjectDefined) {
            achievements.add("📋 Defined your capstone project")
        }
        
        if (projectProgress.tasksCompleted > 0) {
            achievements.add("✅ Completed ${projectProgress.tasksCompleted} project task${if (projectProgress.tasksCompleted > 1) "s" else ""}")
        }
        
        if (projectProgress.decisionLogCount > 0) {
            achievements.add("🧠 Logged ${projectProgress.decisionLogCount} technical decision${if (projectProgress.decisionLogCount > 1) "s" else ""}")
        }
        
        // Milestone achievements
        val totalCompleted = learningRepository.getAllNodes().first().count { it.status == NodeStatus.COMPLETED }
        when (totalCompleted) {
            5 -> achievements.add("🌟 Reached 5 completed modules!")
            10 -> achievements.add("💫 Double digits - 10 modules completed!")
            15 -> achievements.add("🏆 All modules completed - you're an AI expert!")
        }
        
        return achievements.ifEmpty { listOf("Keep up the consistent learning!") }
    }
    
    private suspend fun generateNextWeekFocus(
        allNodes: List<com.codex.aijourney.data.model.LearningNode>,
        stages: List<com.codex.aijourney.data.model.LearningStage>,
        currentStage: com.codex.aijourney.data.model.LearningStage?
    ): String {
        val inProgressNodes = allNodes.filter { it.status == NodeStatus.IN_PROGRESS }
        val todoNodes = allNodes.filter { it.status == NodeStatus.TODO }
        
        return when {
            inProgressNodes.isNotEmpty() -> {
                val node = inProgressNodes.first()
                "Continue working on '${node.title}' and apply the concepts to your capstone project."
            }
            
            todoNodes.isNotEmpty() -> {
                val node = todoNodes.first()
                val stage = stages.find { it.id == node.stageId }
                val stageType = StageType.values().find { it.id == stage?.id }
                
                when (stageType) {
                    StageType.FOUNDATIONS -> "Focus on '${node.title}' to build your programming foundation."
                    StageType.ML_PRACTITIONER -> "Dive into '${node.title}' to strengthen your ML skills."
                    StageType.GENAI_SPECIALIST -> "Explore '${node.title}' to master generative AI concepts."
                    StageType.ARCHITECT_ENGINEER -> "Study '${node.title}' to learn system design principles."
                    StageType.PRODUCT_VISIONARY -> "Work on '${node.title}' to develop product leadership skills."
                    else -> "Start '${node.title}' to continue your learning journey."
                }
            }
            
            else -> {
                "Congratulations! You've completed all learning modules. Focus on finishing your capstone project and preparing for your next career step."
            }
        }
    }
    
    companion object {
        const val WORK_NAME = "weekly_digest_work"
    }
}
