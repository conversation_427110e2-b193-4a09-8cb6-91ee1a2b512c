package com.codex.aijourney.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.codex.aijourney.data.model.LearningNode
import com.codex.aijourney.data.model.LearningStage
import com.codex.aijourney.data.model.StageType

@Composable
fun LearningStageCard(
    stage: LearningStage,
    nodes: List<LearningNode>,
    progress: Float,
    onNodeClick: (String) -> Unit,
    onNodeLongPress: (LearningNode) -> Unit,
    modifier: Modifier = Modifier
) {
    val stageType = StageType.values().find { it.id == stage.id }
    val stageColor = Color(android.graphics.Color.parseColor(stageType?.color ?: "#2196F3"))
    
    // Animation for progress bar
    val animatedProgress by animateFloatAsState(
        targetValue = progress / 100f,
        animationSpec = tween(durationMillis = 1000, easing = EaseOutCubic),
        label = "progress_animation"
    )
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (stage.isUnlocked) Color.White else Color(0xFFF5F5F5)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Stage header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stage.title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = if (stage.isUnlocked) stageColor else Color.Gray
                    )
                    Text(
                        text = stage.description,
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                // Stage status indicator
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            color = if (stage.isUnlocked) stageColor else Color.Gray,
                            shape = RoundedCornerShape(20.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (stage.isCompleted) "✓" else "${stage.id}",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                }
            }
            
            // Progress bar
            if (stage.isUnlocked) {
                Column(
                    modifier = Modifier.padding(top = 12.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Progress",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        Text(
                            text = "${progress.toInt()}%",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = stageColor
                        )
                    }
                    
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(6.dp)
                            .padding(top = 4.dp)
                            .clip(RoundedCornerShape(3.dp))
                            .background(Color(0xFFE0E0E0))
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .fillMaxWidth(animatedProgress)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            stageColor.copy(alpha = 0.7f),
                                            stageColor
                                        )
                                    ),
                                    shape = RoundedCornerShape(3.dp)
                                )
                        )
                    }
                }
            }
            
            // Nodes grid
            if (stage.isUnlocked && nodes.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Learning Modules",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // Simple grid layout using Column and Row
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    nodes.chunked(2).forEach { rowNodes ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            rowNodes.forEach { node ->
                                Box(modifier = Modifier.weight(1f)) {
                                    SkillTreeNode(
                                        node = node,
                                        onClick = { onNodeClick(node.id) },
                                        onLongPress = { onNodeLongPress(node) },
                                        showConnections = false
                                    )
                                }
                            }
                            // Fill remaining space if odd number of nodes
                            if (rowNodes.size == 1) {
                                Spacer(modifier = Modifier.weight(1f))
                            }
                        }
                    }
                }
            } else if (!stage.isUnlocked) {
                // Locked stage message
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "🔒",
                            fontSize = 24.sp,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        Text(
                            text = "Complete previous stage to unlock",
                            fontSize = 12.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}


