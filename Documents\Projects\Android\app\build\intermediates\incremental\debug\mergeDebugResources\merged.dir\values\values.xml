<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools" xmlns:ns2="urn:oasis:names:tc:xliff:document:1.2">
    <attr format="reference" name="nestedScrollViewStyle"/>
    <bool name="enable_system_alarm_service_default">true</bool>
    <bool name="enable_system_foreground_service_default">true</bool>
    <bool name="enable_system_job_service_default">false</bool>
    <bool name="workmanager_test_configuration">false</bool>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="black">#FF000000</color>
    <color name="call_notification_answer_color">#1d873b</color>
    <color name="call_notification_decline_color">#d93025</color>
    <color name="md_theme_dark_background">#1c1b1f</color>
    <color name="md_theme_dark_error">#ffb4ab</color>
    <color name="md_theme_dark_errorContainer">#93000a</color>
    <color name="md_theme_dark_onBackground">#e6e1e5</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#ffdad6</color>
    <color name="md_theme_dark_onPrimary">#381e72</color>
    <color name="md_theme_dark_onPrimaryContainer">#eaddff</color>
    <color name="md_theme_dark_onSecondary">#332d41</color>
    <color name="md_theme_dark_onSecondaryContainer">#e8def8</color>
    <color name="md_theme_dark_onSurface">#e6e1e5</color>
    <color name="md_theme_dark_onSurfaceVariant">#cab6d0</color>
    <color name="md_theme_dark_onTertiary">#492532</color>
    <color name="md_theme_dark_onTertiaryContainer">#ffd8e4</color>
    <color name="md_theme_dark_outline">#938f99</color>
    <color name="md_theme_dark_outlineVariant">#49454f</color>
    <color name="md_theme_dark_primary">#d0bcff</color>
    <color name="md_theme_dark_primaryContainer">#4f378b</color>
    <color name="md_theme_dark_scrim">#000000</color>
    <color name="md_theme_dark_secondary">#ccc2dc</color>
    <color name="md_theme_dark_secondaryContainer">#4a4458</color>
    <color name="md_theme_dark_surface">#1c1b1f</color>
    <color name="md_theme_dark_surfaceVariant">#49454f</color>
    <color name="md_theme_dark_tertiary">#efb8c8</color>
    <color name="md_theme_dark_tertiaryContainer">#633b48</color>
    <color name="md_theme_light_background">#fffbfe</color>
    <color name="md_theme_light_error">#ba1a1a</color>
    <color name="md_theme_light_errorContainer">#ffdad6</color>
    <color name="md_theme_light_onBackground">#1c1b1f</color>
    <color name="md_theme_light_onError">#ffffff</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#ffffff</color>
    <color name="md_theme_light_onPrimaryContainer">#21005d</color>
    <color name="md_theme_light_onSecondary">#ffffff</color>
    <color name="md_theme_light_onSecondaryContainer">#1d192b</color>
    <color name="md_theme_light_onSurface">#1c1b1f</color>
    <color name="md_theme_light_onSurfaceVariant">#49454f</color>
    <color name="md_theme_light_onTertiary">#ffffff</color>
    <color name="md_theme_light_onTertiaryContainer">#31111d</color>
    <color name="md_theme_light_outline">#79747e</color>
    <color name="md_theme_light_outlineVariant">#cab6d0</color>
    <color name="md_theme_light_primary">#6650a4</color>
    <color name="md_theme_light_primaryContainer">#eaddff</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_secondary">#625b71</color>
    <color name="md_theme_light_secondaryContainer">#e8def8</color>
    <color name="md_theme_light_surface">#fffbfe</color>
    <color name="md_theme_light_surfaceVariant">#e7e0ec</color>
    <color name="md_theme_light_tertiary">#7d5260</color>
    <color name="md_theme_light_tertiaryContainer">#ffd8e4</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="seed">#6650a4</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">2dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="androidx_compose_ui_view_composition_context" type="id"/>
    <item name="compose_view_saveable_id_tag" type="id"/>
    <item name="consume_window_insets_tag" type="id"/>
    <item name="fragment_container_view_tag" type="id"/>
    <item name="hide_in_inspector_tag" type="id"/>
    <item name="inspection_slot_table_set" type="id"/>
    <item name="is_pooling_container_tag" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="nav_controller_view_tag" type="id"/>
    <item name="pooling_container_listener_holder_tag" type="id"/>
    <item name="report_drawn" type="id"/>
    <item name="special_effects_controller_view_tag" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_on_apply_window_listener" type="id"/>
    <item name="tag_on_receive_content_listener" type="id"/>
    <item name="tag_on_receive_content_mime_types" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_state_description" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="tag_window_insets_animation_callback" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="title" type="id"/>
    <id name="view_tree_lifecycle_owner"/>
    <id name="view_tree_on_back_pressed_dispatcher_owner"/>
    <id name="view_tree_saved_state_registry_owner"/>
    <id name="view_tree_view_model_store_owner"/>
    <item name="visible_removing_fragment_view_tag" type="id"/>
    <item name="wrapped_composition_tag" type="id"/>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="action_add_to_capstone">Add to Capstone</string>
    <string name="action_go_to_resource">Go to Resource</string>
    <string name="action_mark_complete">Mark Complete</string>
    <string name="action_mark_in_progress">Mark In Progress</string>
    <string name="androidx_startup" translatable="false">androidx.startup</string>
    <string name="app_name">Codex AI Journey Planner</string>
    <string name="back">Back</string>
    <string name="bottom_sheet_collapse_description">Collapse bottom sheet</string>
    <string name="bottom_sheet_dismiss_description">Dismiss bottom sheet</string>
    <string name="bottom_sheet_drag_handle_description">Drag handle</string>
    <string name="bottom_sheet_expand_description">Expand bottom sheet</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string name="cancel">Cancel</string>
    <string name="capstone_hub_title">Capstone Hub</string>
    <string name="close_drawer">"Close navigation menu"</string>
    <string name="close_sheet">"Close sheet"</string>
    <string name="collapsed">Collapsed</string>
    <string name="date_input_headline">Entered date</string>
    <string name="date_input_headline_description">Entered date: %1$s</string>
    <string name="date_input_invalid_for_pattern">Date does not match expected pattern: %1$s</string>
    <string name="date_input_invalid_not_allowed">Date not allowed: %1$s</string>
    <string name="date_input_invalid_year_range">
        Date out of expected year range %1$s - %2$s
    </string>
    <string name="date_input_label">Date</string>
    <string name="date_input_no_input_description">None</string>
    <string name="date_input_title">Select date</string>
    <string name="date_picker_headline">"Selected date"</string>
    <string name="date_picker_headline_description">Current selection: %1$s</string>
    <string name="date_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="date_picker_no_selection_description">None</string>
    <string name="date_picker_scroll_to_earlier_years">Scroll to show earlier years</string>
    <string name="date_picker_scroll_to_later_years">Scroll to show later years</string>
    <string name="date_picker_switch_to_calendar_mode">Switch to calendar input mode</string>
    <string name="date_picker_switch_to_day_selection">
        "Swipe to select a year, or tap to switch back to selecting a day"
    </string>
    <string name="date_picker_switch_to_input_mode">Switch to text input mode</string>
    <string name="date_picker_switch_to_next_month">"Change to next month"</string>
    <string name="date_picker_switch_to_previous_month">"Change to previous month"</string>
    <string name="date_picker_switch_to_year_selection">"Switch to selecting a year"</string>
    <string name="date_picker_title">"Select date"</string>
    <string name="date_picker_today_description">Today</string>
    <string name="date_picker_year_picker_pane_title">Year picker visible</string>
    <string name="date_range_input_invalid_range_input">Invalid date range input</string>
    <string name="date_range_input_title">Enter dates</string>
    <string name="date_range_picker_day_in_range">In range</string>
    <string name="date_range_picker_end_headline">End date</string>
    <string name="date_range_picker_scroll_to_next_month">Scroll to show the next month</string>
    <string name="date_range_picker_scroll_to_previous_month">Scroll to show the previous month</string>
    <string name="date_range_picker_start_headline">Start date</string>
    <string name="date_range_picker_title">Select dates</string>
    <string name="decisions_log_title">Decisions Log</string>
    <string name="default_error_message">"Invalid input"</string>
    <string name="default_popup_window_title" ns1:ignore="ExtraTranslation">"Pop-Up Window"</string>
    <string name="delete">Delete</string>
    <string name="dialog">"Dialog"</string>
    <string name="dropdown_menu">"Dropdown menu"</string>
    <string name="edit">Edit</string>
    <string name="expanded">Expanded</string>
    <string name="in_progress">In progress</string>
    <string name="indeterminate">Partially checked</string>
    <string name="m3c_bottom_sheet_pane_title">Bottom Sheet</string>
    <string name="navigation_menu">"Navigation menu"</string>
    <string name="node_detail_title">Node Details</string>
    <string name="not_selected">Not selected</string>
    <string name="off">Off</string>
    <string name="on">On</string>
    <string name="prd_title">One-Page PRD</string>
    <string name="project_tasks_title">Project Tasks</string>
    <string name="range_end">"Range end"</string>
    <string name="range_start">"Range start"</string>
    <string name="roadmap_subtitle">Your AI Learning Journey</string>
    <string name="roadmap_title">Learning Roadmap</string>
    <string name="save">Save</string>
    <string name="search_bar_search">Search</string>
    <string name="selected">Selected</string>
    <string name="snackbar_dismiss">Dismiss</string>
    <string name="stage_architect_engineer">The Architect &amp; Engineer</string>
    <string name="stage_foundations">The Twin Foundations</string>
    <string name="stage_genai_specialist">The GenAI Specialist</string>
    <string name="stage_ml_practitioner">The ML Practitioner</string>
    <string name="stage_product_visionary">The Product Visionary</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="status_completed">Completed</string>
    <string name="status_in_progress">In Progress</string>
    <string name="status_locked">Locked</string>
    <string name="status_todo">To Do</string>
    <string name="suggestions_available">Suggestions below</string>
    <string name="switch_role">Switch</string>
    <string name="tab">Tab</string>
    <string name="template_percent"><ns2:g id="percentage">%1$d</ns2:g> percent.</string>
    <string name="time_picker_am">AM</string>
    <string name="time_picker_hour">Hour</string>
    <string name="time_picker_hour_24h_suffix">%1$d hours</string>
    <string name="time_picker_hour_selection">Select hour</string>
    <string name="time_picker_hour_suffix">%1$d o\'clock</string>
    <string name="time_picker_hour_text_field">for hour</string>
    <string name="time_picker_minute">Minute</string>
    <string name="time_picker_minute_selection">Select minutes</string>
    <string name="time_picker_minute_suffix">%1$d minutes</string>
    <string name="time_picker_minute_text_field">for minutes</string>
    <string name="time_picker_period_toggle_description">Select AM or PM</string>
    <string name="time_picker_pm">PM</string>
    <string name="tooltip_long_press_label">Show tooltip</string>
    <string name="tooltip_pane_description">Tooltip</string>
    <style name="Base.Theme.CodexAIJourneyPlanner" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>

        
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>

        
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
    </style>
    <style name="DialogWindowTheme">
        <item name="android:windowClipToOutline">false</item>
    </style>
    <style name="FloatingDialogTheme">
        <item name="android:windowIsFloating">false</item>
    </style>
    <style name="FloatingDialogWindowTheme">
        <item name="android:windowClipToOutline">false</item>
        <item name="android:dialogTheme">@style/FloatingDialogTheme</item>
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="Theme.CodexAIJourneyPlanner" parent="Base.Theme.CodexAIJourneyPlanner"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <declare-styleable name="ActivityNavigator">
        <attr name="android:name"/>
        <attr format="string" name="action"/>
        <attr format="string" name="data"/>
        <attr format="string" name="dataPattern"/>
        <attr format="string" name="targetPackage"/>
    </declare-styleable>
    <declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="NavAction">
        <attr name="android:id"/>
        <attr format="reference" name="destination"/>
        <attr format="boolean" name="launchSingleTop"/>
        <attr format="boolean" name="restoreState"/>
        <attr format="reference" name="popUpTo"/>
        <attr format="boolean" name="popUpToInclusive"/>
        <attr format="boolean" name="popUpToSaveState"/>
        <attr format="reference" name="enterAnim"/>
        <attr format="reference" name="exitAnim"/>
        <attr format="reference" name="popEnterAnim"/>
        <attr format="reference" name="popExitAnim"/>
    </declare-styleable>
    <declare-styleable name="NavArgument">
        <attr name="android:name"/>
        <attr name="android:defaultValue"/>
        <attr format="boolean" name="nullable"/>
        <attr format="string" name="argType"/>
    </declare-styleable>
    <declare-styleable name="NavDeepLink">
        <attr format="string" name="uri"/>
        <attr format="string" name="action"/>
        <attr format="string" name="mimeType"/>
        <attr name="android:autoVerify"/>
    </declare-styleable>
    <declare-styleable name="NavGraphNavigator">
        <attr format="reference" name="startDestination"/>
    </declare-styleable>
    <declare-styleable name="NavHost">
        <attr format="reference" name="navGraph"/>
    </declare-styleable>
    <declare-styleable name="NavInclude">
        <attr format="reference" name="graph"/>
    </declare-styleable>
    <declare-styleable name="Navigator">
        <attr name="android:id"/>
        <attr format="string" name="route"/>
        <attr name="android:label"/>
    </declare-styleable>
</resources>