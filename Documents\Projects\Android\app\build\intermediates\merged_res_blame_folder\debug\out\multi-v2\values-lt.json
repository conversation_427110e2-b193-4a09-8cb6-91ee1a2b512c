{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "563,661,771,870,973,1084,1194,6487", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "656,766,865,968,1079,1189,1309,6583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,336,449,1491,1569,1659,1767,1895,2007,2147,2227,2322,2414,2509,2630,2744,2844,2979,3110,3245,3437,3563,3677,3800,3924,4017,4114,4232,4357,4451,4550,4653,4786,4930,5035,5337,5512,6242,6401,6588,6980,7063,7146,7242,7347,7439,7534,7618,7725,7817,7912,8046,8126,8225", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "215,331,444,558,1564,1654,1762,1890,2002,2142,2222,2317,2409,2504,2625,2739,2839,2974,3105,3240,3432,3558,3672,3795,3919,4012,4109,4227,4352,4446,4545,4648,4781,4925,5030,5129,5412,5585,6321,6482,6690,7058,7141,7237,7342,7434,7529,7613,7720,7812,7907,8041,8121,8220,8313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1314,1407,5134,5232,5417,5590,5667,5758,5845,5929,5999,6068,6154,6326,6695,6775,6858", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "1402,1486,5227,5332,5507,5662,5753,5840,5924,5994,6063,6149,6237,6396,6770,6853,6975"}}]}]}