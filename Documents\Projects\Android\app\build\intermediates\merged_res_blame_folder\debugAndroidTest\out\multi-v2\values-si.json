{"logs": [{"outputFile": "com.codex.aijourney.test.app-mergeDebugAndroidTestResources-32:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,926,1009,1108,1207,1289,1374,1465,1551,1631,1708,1783,1862,1944,2118,2199,2266", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "921,1004,1103,1202,1284,1369,1460,1546,1626,1703,1778,1857,1939,2012,2194,2261,2379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,415,520,619,723,2017", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "202,305,410,515,614,718,832,2113"}}]}]}