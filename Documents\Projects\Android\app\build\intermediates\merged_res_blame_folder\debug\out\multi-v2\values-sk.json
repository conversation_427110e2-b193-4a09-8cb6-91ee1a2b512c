{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1281,1376,5053,5148,5331,5500,5579,5673,5763,5844,5913,5982,6065,6241,6594,6672,6740", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "1371,1455,5143,5246,5418,5574,5668,5758,5839,5908,5977,6060,6147,6308,6667,6735,6849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "544,640,742,843,941,1051,1159,6396", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "635,737,838,936,1046,1154,1276,6492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,383,494,570,661,770,901,1013,1161,1242,1339,1427,1520,1632,1751,1853,1982,2111,2241,2401,2524,2644,2771,2888,2977,3070,3187,3305,3401,3500,3605,3741,3886,3991,4087,4167,4244,4333,4416,4513,4589,4671,4764,4863,4952,5045,5129,5230,5323,5417,5533,5609,5706", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "161,272,378,489,565,656,765,896,1008,1156,1237,1334,1422,1515,1627,1746,1848,1977,2106,2236,2396,2519,2639,2766,2883,2972,3065,3182,3300,3396,3495,3600,3736,3881,3986,4082,4162,4239,4328,4411,4508,4584,4666,4759,4858,4947,5040,5124,5225,5318,5412,5528,5604,5701,5790"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,433,1460,1536,1627,1736,1867,1979,2127,2208,2305,2393,2486,2598,2717,2819,2948,3077,3207,3367,3490,3610,3737,3854,3943,4036,4153,4271,4367,4466,4571,4707,4852,4957,5251,5423,6152,6313,6497,6854,6930,7012,7105,7204,7293,7386,7470,7571,7664,7758,7874,7950,8047", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "211,322,428,539,1531,1622,1731,1862,1974,2122,2203,2300,2388,2481,2593,2712,2814,2943,3072,3202,3362,3485,3605,3732,3849,3938,4031,4148,4266,4362,4461,4566,4702,4847,4952,5048,5326,5495,6236,6391,6589,6925,7007,7100,7199,7288,7381,7465,7566,7659,7753,7869,7945,8042,8131"}}]}]}