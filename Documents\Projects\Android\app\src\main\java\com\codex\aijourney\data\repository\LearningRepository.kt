package com.codex.aijourney.data.repository

import com.codex.aijourney.data.dao.LearningDao
import com.codex.aijourney.data.model.*
import com.codex.aijourney.notifications.NotificationHelper
import com.codex.aijourney.notifications.NotificationScheduler
import com.codex.aijourney.data.preferences.NotificationPreferences
import kotlinx.coroutines.flow.Flow
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LearningRepository @Inject constructor(
    private val learningDao: LearningDao,
    private val notificationHelper: NotificationHelper,
    private val notificationScheduler: NotificationScheduler,
    private val notificationPreferences: NotificationPreferences
) {
    
    // Stages
    fun getAllStages(): Flow<List<LearningStage>> = learningDao.getAllStages()
    
    suspend fun getStageById(stageId: Int): LearningStage? = learningDao.getStageById(stageId)
    
    suspend fun updateStage(stage: LearningStage) = learningDao.updateStage(stage)
    
    // Nodes
    fun getNodesByStage(stageId: Int): Flow<List<LearningNode>> = learningDao.getNodesByStage(stageId)
    
    fun getAllNodes(): Flow<List<LearningNode>> = learningDao.getAllNodes()
    
    suspend fun getNodeById(nodeId: String): LearningNode? = learningDao.getNodeById(nodeId)
    
    suspend fun updateNode(node: LearningNode) = learningDao.updateNode(node)
    
    suspend fun updateNodeStatus(nodeId: String, status: NodeStatus) {
        val node = getNodeById(nodeId)
        node?.let {
            val updatedNode = it.copy(
                status = status,
                completedAt = if (status == NodeStatus.COMPLETED) System.currentTimeMillis() else null
            )
            updateNode(updatedNode)

            // Trigger smart notifications based on status change
            handleStatusChangeNotifications(updatedNode, status)
        }
    }
    
    // Notes
    fun getNotesByNode(nodeId: String): Flow<List<UserNote>> = learningDao.getNotesByNode(nodeId)
    
    suspend fun insertNote(note: UserNote) = learningDao.insertNote(note)
    
    suspend fun updateNote(note: UserNote) = learningDao.updateNote(note)
    
    suspend fun deleteNote(note: UserNote) = learningDao.deleteNote(note)
    
    // Data seeding
    suspend fun seedInitialData() {
        // Check if data already exists
        val existingStages = learningDao.getAllStages()
        
        // Seed stages
        val stages = StageType.values().map { stageType ->
            LearningStage(
                id = stageType.id,
                title = stageType.title,
                description = stageType.description,
                order = stageType.id,
                isUnlocked = stageType.id == 1 // Only first stage unlocked initially
            )
        }
        learningDao.insertStages(stages)
        
        // Seed nodes for each stage
        seedStageNodes()
    }
    
    private suspend fun seedStageNodes() {
        val allNodes = mutableListOf<LearningNode>()
        
        // Stage 1: The Twin Foundations
        allNodes.addAll(createFoundationNodes())
        
        // Stage 2: The ML Practitioner  
        allNodes.addAll(createMLPractitionerNodes())
        
        // Stage 3: The GenAI Specialist
        allNodes.addAll(createGenAINodes())
        
        // Stage 4: The Architect & Engineer
        allNodes.addAll(createArchitectNodes())
        
        // Stage 5: The Product Visionary
        allNodes.addAll(createProductNodes())
        
        learningDao.insertNodes(allNodes)
    }

    private fun createFoundationNodes(): List<LearningNode> {
        return listOf(
            LearningNode(
                id = "python_fundamentals",
                stageId = 1,
                title = "Python Fundamentals",
                description = "Master Python basics, data structures, and OOP",
                resourceUrl = "https://docs.python.org/3/tutorial/",
                order = 1,
                status = NodeStatus.TODO,
                estimatedHours = 40
            ),
            LearningNode(
                id = "data_structures",
                stageId = 1,
                title = "Data Structures & Algorithms",
                description = "Essential CS fundamentals for technical interviews",
                resourceUrl = "https://leetcode.com/",
                order = 2,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("python_fundamentals"),
                estimatedHours = 60
            ),
            LearningNode(
                id = "system_design_basics",
                stageId = 1,
                title = "System Design Fundamentals",
                description = "Learn scalable system architecture principles",
                resourceUrl = "https://github.com/donnemartin/system-design-primer",
                order = 3,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("data_structures"),
                estimatedHours = 50
            )
        )
    }
    
    private fun createMLPractitionerNodes(): List<LearningNode> {
        return listOf(
            LearningNode(
                id = "ml_mathematics",
                stageId = 2,
                title = "ML Mathematics",
                description = "Linear algebra, statistics, and calculus for ML",
                resourceUrl = "https://www.khanacademy.org/math/linear-algebra",
                order = 1,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("system_design_basics"),
                estimatedHours = 45
            ),
            LearningNode(
                id = "sklearn_fundamentals",
                stageId = 2,
                title = "Scikit-learn Mastery",
                description = "Classical ML algorithms and model evaluation",
                resourceUrl = "https://scikit-learn.org/stable/tutorial/",
                order = 2,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("ml_mathematics"),
                estimatedHours = 35
            ),
            LearningNode(
                id = "deep_learning_basics",
                stageId = 2,
                title = "Deep Learning Foundations",
                description = "Neural networks with PyTorch/TensorFlow",
                resourceUrl = "https://pytorch.org/tutorials/",
                order = 3,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("sklearn_fundamentals"),
                estimatedHours = 55
            )
        )
    }

    private fun createGenAINodes(): List<LearningNode> {
        return listOf(
            LearningNode(
                id = "transformer_architecture",
                stageId = 3,
                title = "Transformer Architecture",
                description = "Understanding attention mechanisms and transformers",
                resourceUrl = "https://huggingface.co/course/chapter1/1",
                order = 1,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("deep_learning_basics"),
                estimatedHours = 30
            ),
            LearningNode(
                id = "huggingface_course",
                stageId = 3,
                title = "Hugging Face Course",
                description = "Complete NLP course with transformers",
                resourceUrl = "https://huggingface.co/course",
                order = 2,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("transformer_architecture"),
                estimatedHours = 40
            ),
            LearningNode(
                id = "llm_fine_tuning",
                stageId = 3,
                title = "LLM Fine-tuning",
                description = "Fine-tune large language models for specific tasks",
                resourceUrl = "https://huggingface.co/docs/transformers/training",
                order = 3,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("huggingface_course"),
                estimatedHours = 45
            )
        )
    }

    private fun createArchitectNodes(): List<LearningNode> {
        return listOf(
            LearningNode(
                id = "advanced_system_design",
                stageId = 4,
                title = "Advanced System Design",
                description = "Microservices, distributed systems, and scalability",
                resourceUrl = "https://github.com/karanpratapsingh/system-design",
                order = 1,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("llm_fine_tuning"),
                estimatedHours = 50
            ),
            LearningNode(
                id = "mlops_fundamentals",
                stageId = 4,
                title = "MLOps & Deployment",
                description = "Model deployment, monitoring, and CI/CD for ML",
                resourceUrl = "https://mlops.org/",
                order = 2,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("advanced_system_design"),
                estimatedHours = 40
            ),
            LearningNode(
                id = "cloud_platforms",
                stageId = 4,
                title = "Cloud Platforms",
                description = "AWS/GCP/Azure for ML workloads",
                resourceUrl = "https://aws.amazon.com/machine-learning/",
                order = 3,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("mlops_fundamentals"),
                estimatedHours = 35
            )
        )
    }

    private fun createProductNodes(): List<LearningNode> {
        return listOf(
            LearningNode(
                id = "product_management",
                stageId = 5,
                title = "Product Management Fundamentals",
                description = "User research, roadmapping, and product strategy",
                resourceUrl = "https://www.coursera.org/learn/uva-darden-product-management",
                order = 1,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("cloud_platforms"),
                estimatedHours = 30
            ),
            LearningNode(
                id = "ai_product_strategy",
                stageId = 5,
                title = "AI Product Strategy",
                description = "Building and launching AI-powered products",
                resourceUrl = "https://www.deeplearning.ai/courses/ai-for-everyone/",
                order = 2,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("product_management"),
                estimatedHours = 25
            ),
            LearningNode(
                id = "leadership_skills",
                stageId = 5,
                title = "Technical Leadership",
                description = "Leading engineering teams and technical decisions",
                resourceUrl = "https://www.oreilly.com/library/view/the-managers-path/9781491973882/",
                order = 3,
                status = NodeStatus.LOCKED,
                prerequisiteNodeIds = listOf("ai_product_strategy"),
                estimatedHours = 35
            )
        )
    }

    // Smart notification methods
    private suspend fun handleStatusChangeNotifications(node: LearningNode, newStatus: NodeStatus) {
        if (!notificationPreferences.shouldShowNotification()) return

        when (newStatus) {
            NodeStatus.COMPLETED -> {
                if (notificationPreferences.celebrationNotificationsEnabled) {
                    // Immediate celebration
                    notificationHelper.showProgressUpdate(
                        title = "🎉 Module Completed!",
                        message = "Great job completing '${node.title}'! You're making excellent progress."
                    )

                    // Schedule follow-up nudge to apply learning
                    notificationScheduler.scheduleContextualNudge(
                        delayHours = 2,
                        message = "Ready to apply what you learned from '${node.title}' to your capstone project?"
                    )
                }

                // Check for stage completion
                checkStageCompletionNotification(node.stageId)
            }

            NodeStatus.IN_PROGRESS -> {
                if (notificationPreferences.smartNudgesEnabled) {
                    // Schedule reminder to continue learning
                    notificationScheduler.scheduleContextualNudge(
                        delayHours = 24,
                        message = "How's your progress on '${node.title}'? Even 15 minutes today can help!"
                    )
                }
            }

            else -> { /* No notification for other status changes */ }
        }

        notificationPreferences.markNotificationShown()
    }

    private suspend fun checkStageCompletionNotification(stageId: Int) {
        val allNodes = getAllNodes().first()
        val stageNodes = allNodes.filter { it.stageId == stageId }
        val completedNodes = stageNodes.filter { it.status == NodeStatus.COMPLETED }

        if (stageNodes.isNotEmpty() && completedNodes.size == stageNodes.size) {
            // Stage completed!
            val stage = getStageById(stageId)
            stage?.let {
                notificationHelper.showProgressUpdate(
                    title = "🏆 Stage Completed!",
                    message = "Congratulations! You've completed the '${it.title}' stage. You're becoming an AI expert!",
                    progress = 100
                )

                // Schedule celebration and next stage preview
                notificationScheduler.scheduleProgressCelebration(
                    achievement = "Completed ${it.title} stage",
                    delayMinutes = 30
                )
            }
        }
    }

    suspend fun getDaysSinceLastActivity(): Int {
        val allNodes = getAllNodes().first()
        val lastActivity = allNodes
            .mapNotNull { it.completedAt }
            .maxOrNull() ?: return Int.MAX_VALUE

        val daysSince = TimeUnit.MILLISECONDS.toDays(System.currentTimeMillis() - lastActivity)
        return daysSince.toInt()
    }

    suspend fun getCurrentStageTitle(): String {
        val allNodes = getAllNodes().first()
        val stages = getAllStages().first()

        val currentStage = stages.find { stage ->
            val stageNodes = allNodes.filter { it.stageId == stage.id }
            stageNodes.any { it.status == NodeStatus.IN_PROGRESS || it.status == NodeStatus.TODO }
        }

        return currentStage?.title ?: "Learning Journey"
    }

    suspend fun hasInProgressNodes(): Boolean {
        val allNodes = getAllNodes().first()
        return allNodes.any { it.status == NodeStatus.IN_PROGRESS }
    }

    suspend fun scheduleSmartReminders() {
        val daysSinceLastActivity = getDaysSinceLastActivity()
        val hasInProgress = hasInProgressNodes()
        val currentStage = getCurrentStageTitle()

        notificationScheduler.scheduleSmartReminders(
            hasInProgressNodes = hasInProgress,
            daysSinceLastActivity = daysSinceLastActivity,
            currentStage = currentStage
        )
    }
}
