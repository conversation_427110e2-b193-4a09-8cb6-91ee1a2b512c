{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "617,716,818,920,1023,1124,1226,6531", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "711,813,915,1018,1119,1221,1341,6627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,322,437,567,652,747,860,998,1115,1253,1334,1436,1526,1623,1748,1873,1980,2101,2220,2350,2528,2650,2766,2885,3015,3106,3197,3326,3469,3562,3663,3769,3891,4019,4128,4224,4302,4384,4471,4557,4656,4732,4813,4910,5010,5100,5201,5284,5386,5481,5584,5698,5774,5870", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "184,317,432,562,647,742,855,993,1110,1248,1329,1431,1521,1618,1743,1868,1975,2096,2215,2345,2523,2645,2761,2880,3010,3101,3192,3321,3464,3557,3658,3764,3886,4014,4123,4219,4297,4379,4466,4552,4651,4727,4808,4905,5005,5095,5196,5279,5381,5476,5579,5693,5769,5865,5955"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,239,372,487,1524,1609,1704,1817,1955,2072,2210,2291,2393,2483,2580,2705,2830,2937,3058,3177,3307,3485,3607,3723,3842,3972,4063,4154,4283,4426,4519,4620,4726,4848,4976,5085,5369,5533,6275,6445,6632,6992,7068,7149,7246,7346,7436,7537,7620,7722,7817,7920,8034,8110,8206", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "234,367,482,612,1604,1699,1812,1950,2067,2205,2286,2388,2478,2575,2700,2825,2932,3053,3172,3302,3480,3602,3718,3837,3967,4058,4149,4278,4421,4514,4615,4721,4843,4971,5080,5176,5442,5610,6357,6526,6726,7063,7144,7241,7341,7431,7532,7615,7717,7812,7915,8029,8105,8201,8291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1346,1440,5181,5271,5447,5615,5694,5800,5887,5976,6046,6116,6194,6362,6731,6806,6874", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "1435,1519,5266,5364,5528,5689,5795,5882,5971,6041,6111,6189,6270,6440,6801,6869,6987"}}]}]}