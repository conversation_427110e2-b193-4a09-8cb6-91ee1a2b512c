package com.codex.aijourney.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.codex.aijourney.data.model.LearningNode
import com.codex.aijourney.data.model.NodeStatus
import com.codex.aijourney.ui.theme.NodeStatusColors

@Composable
fun SkillTreeNode(
    node: LearningNode,
    onClick: () -> Unit,
    onLongPress: () -> Unit,
    modifier: Modifier = Modifier,
    showConnections: Boolean = true,
    connectionTargets: List<Offset> = emptyList()
) {
    val density = LocalDensity.current
    
    // Animation for node interactions
    var isPressed by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "node_scale"
    )
    
    // Color based on status
    val nodeColor = when (node.status) {
        NodeStatus.LOCKED -> Color(0xFFBDBDBD)
        NodeStatus.TODO -> Color(0xFF2196F3)
        NodeStatus.IN_PROGRESS -> Color(0xFFFF9800)
        NodeStatus.COMPLETED -> Color(0xFF4CAF50)
    }
    
    // Status icon
    val statusIcon = when (node.status) {
        NodeStatus.LOCKED -> "🔒"
        NodeStatus.TODO -> "📚"
        NodeStatus.IN_PROGRESS -> "⚡"
        NodeStatus.COMPLETED -> "✅"
    }
    
    Box(
        modifier = modifier
            .size(120.dp)
            .scale(scale)
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    },
                    onTap = { onClick() },
                    onLongPress = { onLongPress() }
                )
            }
    ) {
        // Connection lines (drawn behind the node)
        if (showConnections && connectionTargets.isNotEmpty()) {
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                connectionTargets.forEach { target ->
                    drawConnectionLine(
                        start = Offset(size.width / 2, size.height / 2),
                        end = target,
                        color = nodeColor.copy(alpha = 0.6f)
                    )
                }
            }
        }
        
        // Main node container
        Card(
            modifier = Modifier
                .fillMaxSize()
                .border(
                    width = if (node.status == NodeStatus.IN_PROGRESS) 3.dp else 1.dp,
                    color = nodeColor,
                    shape = RoundedCornerShape(16.dp)
                ),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (node.status == NodeStatus.LOCKED) 
                    Color(0xFFF5F5F5) else Color.White
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = if (node.status == NodeStatus.COMPLETED) 8.dp else 4.dp
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // Status icon
                Text(
                    text = statusIcon,
                    fontSize = 20.sp,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                
                // Node title
                Text(
                    text = node.title,
                    fontSize = 11.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    color = if (node.status == NodeStatus.LOCKED) 
                        Color.Gray else Color.Black
                )
                
                // Estimated hours
                if (node.estimatedHours > 0) {
                    Text(
                        text = "${node.estimatedHours}h",
                        fontSize = 9.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
            }
        }
        
        // Progress indicator for in-progress nodes
        if (node.status == NodeStatus.IN_PROGRESS) {
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .align(Alignment.TopEnd)
                    .offset(x = 4.dp, y = (-4).dp)
                    .background(
                        color = Color(0xFFFF9800),
                        shape = CircleShape
                    )
                    .border(
                        width = 2.dp,
                        color = Color.White,
                        shape = CircleShape
                    )
            )
        }
    }
}

private fun DrawScope.drawConnectionLine(
    start: Offset,
    end: Offset,
    color: Color,
    strokeWidth: Float = 3.dp.toPx()
) {
    drawLine(
        color = color,
        start = start,
        end = end,
        strokeWidth = strokeWidth
    )
}

@Composable
fun NodeStatusDialog(
    node: LearningNode,
    onStatusChange: (NodeStatus) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = node.title,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = node.description,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = "Change Status:",
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // Status options (only show valid transitions)
                val availableStatuses = getAvailableStatusTransitions(node.status)
                
                availableStatuses.forEach { status ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { 
                                onStatusChange(status)
                                onDismiss()
                            }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = status == node.status,
                            onClick = { 
                                onStatusChange(status)
                                onDismiss()
                            }
                        )
                        Text(
                            text = getStatusDisplayName(status),
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

private fun getAvailableStatusTransitions(currentStatus: NodeStatus): List<NodeStatus> {
    return when (currentStatus) {
        NodeStatus.LOCKED -> emptyList() // Locked nodes can't be manually changed
        NodeStatus.TODO -> listOf(NodeStatus.TODO, NodeStatus.IN_PROGRESS, NodeStatus.COMPLETED)
        NodeStatus.IN_PROGRESS -> listOf(NodeStatus.TODO, NodeStatus.IN_PROGRESS, NodeStatus.COMPLETED)
        NodeStatus.COMPLETED -> listOf(NodeStatus.IN_PROGRESS, NodeStatus.COMPLETED)
    }
}

private fun getStatusDisplayName(status: NodeStatus): String {
    return when (status) {
        NodeStatus.LOCKED -> "Locked"
        NodeStatus.TODO -> "To Do"
        NodeStatus.IN_PROGRESS -> "In Progress"
        NodeStatus.COMPLETED -> "Completed"
    }
}
