# Codex: AI Journey Planner

An Android application for managing your AI learning journey with intelligent project management features.

## Project Structure

### Architecture
- **MVVM (Model-View-ViewModel)** - Clean separation of concerns
- **Jetpack Compose** - Modern declarative UI
- **Room Database** - Local data persistence
- **Hilt** - Dependency injection
- **WorkManager** - Background task scheduling

### Key Features (Planned)
1. **Dynamic Roadmap** - Interactive 5-stage learning skill tree
2. **Node Detail View** - Resource links, notes, and progress tracking
3. **Capstone Hub** - Project management with PRD, decisions log, and tasks
4. **Intelligent Reminders** - Context-aware notifications
5. **Weekly Digest** - Progress summaries and planning

### Database Schema
- **LearningStage** - The 5 main learning stages
- **LearningNode** - Individual learning modules/tasks
- **UserNote** - Personal notes tied to nodes
- **CapstoneProject** - Main project details
- **DecisionLog** - Technical and product decisions
- **ProjectTask** - Project-specific to-do items

### Current Status
✅ Project setup complete
✅ Database schema designed
✅ Navigation structure implemented
✅ Basic screens with placeholders
🔄 Next: Implement Dynamic Roadmap with skill tree visualization

## Development Setup

1. Open in Android Studio
2. Sync Gradle files
3. Run the app on emulator or device

## Next Steps

1. Implement the Dynamic Roadmap with visual skill tree
2. Create detailed Node Detail screens
3. Build the Capstone Hub functionality
4. Add intelligent notification system
5. Implement data seeding for the 5-stage curriculum
