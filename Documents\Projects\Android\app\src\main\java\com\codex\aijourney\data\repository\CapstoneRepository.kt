package com.codex.aijourney.data.repository

import com.codex.aijourney.data.dao.CapstoneDao
import com.codex.aijourney.data.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CapstoneRepository @Inject constructor(
    private val capstoneDao: CapstoneDao
) {
    
    // Capstone Project
    fun getProject(): Flow<CapstoneProject?> = capstoneDao.getProject()
    
    suspend fun createOrUpdateProject(project: CapstoneProject) {
        capstoneDao.insertProject(project)
    }
    
    suspend fun updateProject(project: CapstoneProject) {
        capstoneDao.updateProject(project)
    }
    
    // Decision Logs
    fun getDecisionLogs(): Flow<List<DecisionLog>> = capstoneDao.getDecisionLogs()
    
    suspend fun addDecisionLog(decision: String, reasoning: String = "") {
        val log = DecisionLog(
            id = UUID.randomUUID().toString(),
            decision = decision,
            reasoning = reasoning,
            date = System.currentTimeMillis()
        )
        capstoneDao.insertDecisionLog(log)
    }
    
    suspend fun updateDecisionLog(log: DecisionLog) {
        capstoneDao.updateDecisionLog(log)
    }
    
    suspend fun deleteDecisionLog(log: DecisionLog) {
        capstoneDao.deleteDecisionLog(log)
    }
    
    // Project Tasks
    fun getProjectTasks(): Flow<List<ProjectTask>> = capstoneDao.getProjectTasks()
    
    suspend fun addProjectTask(title: String, description: String = "", priority: TaskPriority = TaskPriority.MEDIUM) {
        val task = ProjectTask(
            id = UUID.randomUUID().toString(),
            title = title,
            description = description,
            priority = priority
        )
        capstoneDao.insertTask(task)
    }
    
    suspend fun updateTask(task: ProjectTask) {
        capstoneDao.updateTask(task)
    }
    
    suspend fun toggleTaskCompletion(taskId: String) {
        val task = capstoneDao.getTaskById(taskId)
        task?.let {
            val updatedTask = it.copy(
                isCompleted = !it.isCompleted,
                completedAt = if (!it.isCompleted) System.currentTimeMillis() else null
            )
            capstoneDao.updateTask(updatedTask)
        }
    }
    
    suspend fun deleteTask(task: ProjectTask) {
        capstoneDao.deleteTask(task)
    }
    
    // Helper methods for node integration
    suspend fun addLearningInsightToCapstone(nodeTitle: String, insight: String) {
        val decisionText = "Learning Insight from '$nodeTitle': $insight"
        addDecisionLog(decisionText, "Key takeaway from learning module")
    }
    
    suspend fun addNodeToProjectTasks(nodeTitle: String, nodeDescription: String) {
        val taskTitle = "Apply learnings from: $nodeTitle"
        val taskDescription = "Implement concepts from '$nodeTitle' in the capstone project.\n\nModule description: $nodeDescription"
        addProjectTask(taskTitle, taskDescription, TaskPriority.MEDIUM)
    }
    
    suspend fun initializeDefaultProject() {
        val existingProject = getProject().first()
        if (existingProject == null) {
            val defaultProject = CapstoneProject(
                title = "My AI Project",
                problem = "Define the problem you want to solve with AI...",
                targetUser = "Describe your target users...",
                mvpFeatures = "List your MVP features..."
            )
            createOrUpdateProject(defaultProject)
            
            // Add some default tasks
            addProjectTask(
                "Define Project Scope",
                "Clearly define the problem, target users, and success metrics",
                TaskPriority.HIGH
            )
            
            addProjectTask(
                "Research Dataset",
                "Find and evaluate potential datasets for your AI model",
                TaskPriority.HIGH
            )
            
            addProjectTask(
                "Create Project Repository",
                "Set up version control and project structure",
                TaskPriority.MEDIUM
            )
        }
    }
    
    suspend fun getProjectProgress(): ProjectProgress {
        val tasks = getProjectTasks().first()
        val completedTasks = tasks.count { it.isCompleted }
        val totalTasks = tasks.size
        
        val decisionLogs = getDecisionLogs().first()
        val project = getProject().first()
        
        val isProjectDefined = project?.let {
            it.title.isNotBlank() && 
            it.problem.isNotBlank() && 
            it.targetUser.isNotBlank() && 
            it.mvpFeatures.isNotBlank()
        } ?: false
        
        return ProjectProgress(
            tasksCompleted = completedTasks,
            totalTasks = totalTasks,
            decisionLogCount = decisionLogs.size,
            isProjectDefined = isProjectDefined,
            completionPercentage = if (totalTasks > 0) (completedTasks.toFloat() / totalTasks) * 100f else 0f
        )
    }
}

data class ProjectProgress(
    val tasksCompleted: Int,
    val totalTasks: Int,
    val decisionLogCount: Int,
    val isProjectDefined: Boolean,
    val completionPercentage: Float
)
