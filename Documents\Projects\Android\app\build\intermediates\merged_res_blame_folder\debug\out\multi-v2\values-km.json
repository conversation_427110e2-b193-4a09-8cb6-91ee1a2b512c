{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1269,1354,5077,5181,5351,5517,5601,5684,5769,5856,5921,5986,6066,6235,6593,6677,6743", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "1349,1429,5176,5274,5434,5596,5679,5764,5851,5916,5981,6061,6146,6306,6672,6738,6856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,643,746,844,944,1045,1157,6395", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "638,741,839,939,1040,1152,1264,6491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,386,498,577,679,799,939,1063,1211,1298,1394,1490,1596,1716,1837,1938,2060,2182,2306,2464,2582,2692,2802,2922,3019,3115,3237,3372,3472,3573,3681,3801,3925,4038,4141,4213,4291,4375,4459,4556,4632,4712,4808,4906,4998,5103,5186,5284,5378,5484,5597,5673,5776", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "164,279,381,493,572,674,794,934,1058,1206,1293,1389,1485,1591,1711,1832,1933,2055,2177,2301,2459,2577,2687,2797,2917,3014,3110,3232,3367,3467,3568,3676,3796,3920,4033,4136,4208,4286,4370,4454,4551,4627,4707,4803,4901,4993,5098,5181,5279,5373,5479,5592,5668,5771,5867"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,334,436,1434,1513,1615,1735,1875,1999,2147,2234,2330,2426,2532,2652,2773,2874,2996,3118,3242,3400,3518,3628,3738,3858,3955,4051,4173,4308,4408,4509,4617,4737,4861,4974,5279,5439,6151,6311,6496,6861,6937,7017,7113,7211,7303,7408,7491,7589,7683,7789,7902,7978,8081", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "214,329,431,543,1508,1610,1730,1870,1994,2142,2229,2325,2421,2527,2647,2768,2869,2991,3113,3237,3395,3513,3623,3733,3853,3950,4046,4168,4303,4403,4504,4612,4732,4856,4969,5072,5346,5512,6230,6390,6588,6932,7012,7108,7206,7298,7403,7486,7584,7678,7784,7897,7973,8076,8172"}}]}]}