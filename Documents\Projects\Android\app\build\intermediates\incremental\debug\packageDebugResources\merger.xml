<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res"><file name="ic_launcher" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Codex AI Journey Planner</string><string name="roadmap_title">Learning Roadmap</string><string name="node_detail_title">Node Details</string><string name="capstone_hub_title">Capstone Hub</string><string name="roadmap_subtitle">Your AI Learning Journey</string><string name="stage_foundations">The Twin Foundations</string><string name="stage_ml_practitioner">The ML Practitioner</string><string name="stage_genai_specialist">The GenAI Specialist</string><string name="stage_architect_engineer">The Architect &amp; Engineer</string><string name="stage_product_visionary">The Product Visionary</string><string name="status_locked">Locked</string><string name="status_todo">To Do</string><string name="status_in_progress">In Progress</string><string name="status_completed">Completed</string><string name="action_go_to_resource">Go to Resource</string><string name="action_add_to_capstone">Add to Capstone</string><string name="action_mark_complete">Mark Complete</string><string name="action_mark_in_progress">Mark In Progress</string><string name="prd_title">One-Page PRD</string><string name="decisions_log_title">Decisions Log</string><string name="project_tasks_title">Project Tasks</string><string name="save">Save</string><string name="cancel">Cancel</string><string name="edit">Edit</string><string name="delete">Delete</string><string name="back">Back</string></file><file path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.CodexAIJourneyPlanner" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.CodexAIJourneyPlanner" parent="Base.Theme.CodexAIJourneyPlanner"/></file><file name="backup_rules" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Documents\Projects\Android\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\Projects\Android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\Projects\Android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\Projects\Android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\Projects\Android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>