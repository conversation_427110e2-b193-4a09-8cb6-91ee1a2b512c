package com.codex.aijourney.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "learning_stages")
data class LearningStage(
    @PrimaryKey val id: Int,
    val title: String,
    val description: String,
    val order: Int,
    val isUnlocked: Boolean = false,
    val isCompleted: Boolean = false
)

enum class StageType(val id: Int, val title: String, val description: String) {
    FOUNDATIONS(1, "The Twin Foundations", "Master Python and System Design fundamentals"),
    ML_PRACTITIONER(2, "The ML Practitioner", "Build core machine learning expertise"),
    GENAI_SPECIALIST(3, "The GenAI Specialist", "Specialize in Generative AI and LLMs"),
    ARCHITECT_ENGINEER(4, "The Architect & Engineer", "Learn system architecture and engineering"),
    PRODUCT_VISIONARY(5, "The Product Visionary", "Develop product management and leadership skills")
}
