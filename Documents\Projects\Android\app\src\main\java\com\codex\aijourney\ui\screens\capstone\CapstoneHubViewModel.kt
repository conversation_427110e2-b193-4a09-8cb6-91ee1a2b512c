package com.codex.aijourney.ui.screens.capstone

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.codex.aijourney.data.model.*
import com.codex.aijourney.data.repository.CapstoneRepository
import com.codex.aijourney.data.repository.ProjectProgress
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

data class CapstoneHubUiState(
    val project: CapstoneProject? = null,
    val tasks: List<ProjectTask> = emptyList(),
    val decisionLogs: List<DecisionLog> = emptyList(),
    val progress: ProjectProgress = ProjectProgress(0, 0, 0, false, 0f),
    val isLoading: Boolean = true,
    val error: String? = null,
    val isEditingPRD: Boolean = false,
    val isAddingTask: Boolean = false,
    val isAddingDecision: Boolean = false,
    val editingTaskId: String? = null,
    val editingDecisionId: String? = null,
    val newTaskTitle: String = "",
    val newTaskDescription: String = "",
    val newTaskPriority: TaskPriority = TaskPriority.MEDIUM,
    val newDecisionText: String = "",
    val newDecisionReasoning: String = ""
)

@HiltViewModel
class CapstoneHubViewModel @Inject constructor(
    private val capstoneRepository: CapstoneRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(CapstoneHubUiState())
    val uiState: StateFlow<CapstoneHubUiState> = _uiState.asStateFlow()
    
    init {
        loadCapstoneData()
    }
    
    private fun loadCapstoneData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                combine(
                    capstoneRepository.getProject(),
                    capstoneRepository.getProjectTasks(),
                    capstoneRepository.getDecisionLogs(),
                    flow { emit(capstoneRepository.getProjectProgress()) }
                ) { project, tasks, decisions, progress ->
                    CapstoneHubUiState(
                        project = project,
                        tasks = tasks,
                        decisionLogs = decisions,
                        progress = progress,
                        isLoading = false
                    )
                }.catch { exception ->
                    _uiState.value = CapstoneHubUiState(
                        error = "Failed to load capstone data: ${exception.message}",
                        isLoading = false
                    )
                }.collect { newState ->
                    _uiState.value = newState
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load capstone hub: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    // PRD Management
    fun startEditingPRD() {
        _uiState.value = _uiState.value.copy(isEditingPRD = true)
    }
    
    fun cancelEditingPRD() {
        _uiState.value = _uiState.value.copy(isEditingPRD = false)
    }
    
    fun updateProject(project: CapstoneProject) {
        viewModelScope.launch {
            try {
                capstoneRepository.updateProject(project)
                cancelEditingPRD()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update project: ${e.message}"
                )
            }
        }
    }
    
    // Task Management
    fun startAddingTask() {
        _uiState.value = _uiState.value.copy(
            isAddingTask = true,
            editingTaskId = null,
            newTaskTitle = "",
            newTaskDescription = "",
            newTaskPriority = TaskPriority.MEDIUM
        )
    }
    
    fun startEditingTask(task: ProjectTask) {
        _uiState.value = _uiState.value.copy(
            isAddingTask = true,
            editingTaskId = task.id,
            newTaskTitle = task.title,
            newTaskDescription = task.description,
            newTaskPriority = task.priority
        )
    }
    
    fun updateTaskTitle(title: String) {
        _uiState.value = _uiState.value.copy(newTaskTitle = title)
    }
    
    fun updateTaskDescription(description: String) {
        _uiState.value = _uiState.value.copy(newTaskDescription = description)
    }
    
    fun updateTaskPriority(priority: TaskPriority) {
        _uiState.value = _uiState.value.copy(newTaskPriority = priority)
    }
    
    fun saveTask() {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                val title = currentState.newTaskTitle.trim()
                
                if (title.isEmpty()) {
                    cancelAddingTask()
                    return@launch
                }
                
                if (currentState.editingTaskId != null) {
                    // Update existing task
                    val existingTask = currentState.tasks.find { it.id == currentState.editingTaskId }
                    existingTask?.let { task ->
                        val updatedTask = task.copy(
                            title = title,
                            description = currentState.newTaskDescription.trim(),
                            priority = currentState.newTaskPriority
                        )
                        capstoneRepository.updateTask(updatedTask)
                    }
                } else {
                    // Create new task
                    capstoneRepository.addProjectTask(
                        title = title,
                        description = currentState.newTaskDescription.trim(),
                        priority = currentState.newTaskPriority
                    )
                }
                
                cancelAddingTask()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to save task: ${e.message}"
                )
            }
        }
    }
    
    fun cancelAddingTask() {
        _uiState.value = _uiState.value.copy(
            isAddingTask = false,
            editingTaskId = null,
            newTaskTitle = "",
            newTaskDescription = "",
            newTaskPriority = TaskPriority.MEDIUM
        )
    }
    
    fun toggleTaskCompletion(taskId: String) {
        viewModelScope.launch {
            try {
                capstoneRepository.toggleTaskCompletion(taskId)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update task: ${e.message}"
                )
            }
        }
    }
    
    fun deleteTask(task: ProjectTask) {
        viewModelScope.launch {
            try {
                capstoneRepository.deleteTask(task)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete task: ${e.message}"
                )
            }
        }
    }
    
    // Decision Log Management
    fun startAddingDecision() {
        _uiState.value = _uiState.value.copy(
            isAddingDecision = true,
            editingDecisionId = null,
            newDecisionText = "",
            newDecisionReasoning = ""
        )
    }
    
    fun startEditingDecision(decision: DecisionLog) {
        _uiState.value = _uiState.value.copy(
            isAddingDecision = true,
            editingDecisionId = decision.id,
            newDecisionText = decision.decision,
            newDecisionReasoning = decision.reasoning
        )
    }
    
    fun updateDecisionText(text: String) {
        _uiState.value = _uiState.value.copy(newDecisionText = text)
    }
    
    fun updateDecisionReasoning(reasoning: String) {
        _uiState.value = _uiState.value.copy(newDecisionReasoning = reasoning)
    }
    
    fun saveDecision() {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                val decisionText = currentState.newDecisionText.trim()
                
                if (decisionText.isEmpty()) {
                    cancelAddingDecision()
                    return@launch
                }
                
                if (currentState.editingDecisionId != null) {
                    // Update existing decision
                    val existingDecision = currentState.decisionLogs.find { it.id == currentState.editingDecisionId }
                    existingDecision?.let { decision ->
                        val updatedDecision = decision.copy(
                            decision = decisionText,
                            reasoning = currentState.newDecisionReasoning.trim()
                        )
                        capstoneRepository.updateDecisionLog(updatedDecision)
                    }
                } else {
                    // Create new decision
                    capstoneRepository.addDecisionLog(
                        decision = decisionText,
                        reasoning = currentState.newDecisionReasoning.trim()
                    )
                }
                
                cancelAddingDecision()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to save decision: ${e.message}"
                )
            }
        }
    }
    
    fun cancelAddingDecision() {
        _uiState.value = _uiState.value.copy(
            isAddingDecision = false,
            editingDecisionId = null,
            newDecisionText = "",
            newDecisionReasoning = ""
        )
    }
    
    fun deleteDecision(decision: DecisionLog) {
        viewModelScope.launch {
            try {
                capstoneRepository.deleteDecisionLog(decision)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete decision: ${e.message}"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun getTasksByPriority(): Map<TaskPriority, List<ProjectTask>> {
        return _uiState.value.tasks.groupBy { it.priority }
    }
    
    fun getCompletionStats(): Pair<Int, Int> {
        val tasks = _uiState.value.tasks
        return tasks.count { it.isCompleted } to tasks.size
    }
}
