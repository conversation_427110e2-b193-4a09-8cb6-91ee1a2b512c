package com.codex.aijourney.ui.screens.roadmap

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.codex.aijourney.data.model.LearningNode
import com.codex.aijourney.data.model.LearningStage
import com.codex.aijourney.data.model.NodeStatus
import com.codex.aijourney.data.repository.LearningRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class RoadmapUiState(
    val stages: List<LearningStage> = emptyList(),
    val nodesByStage: Map<Int, List<LearningNode>> = emptyMap(),
    val isLoading: Boolean = true,
    val error: String? = null
)

@HiltViewModel
class RoadmapViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(RoadmapUiState())
    val uiState: StateFlow<RoadmapUiState> = _uiState.asStateFlow()
    
    init {
        initializeData()
        loadRoadmapData()
    }
    
    private fun initializeData() {
        viewModelScope.launch {
            try {
                learningRepository.seedInitialData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to initialize data: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    private fun loadRoadmapData() {
        viewModelScope.launch {
            try {
                combine(
                    learningRepository.getAllStages(),
                    learningRepository.getAllNodes()
                ) { stages, nodes ->
                    val nodesByStage = nodes.groupBy { it.stageId }
                    RoadmapUiState(
                        stages = stages,
                        nodesByStage = nodesByStage,
                        isLoading = false
                    )
                }.catch { exception ->
                    _uiState.value = RoadmapUiState(
                        error = "Failed to load data: ${exception.message}",
                        isLoading = false
                    )
                }.collect { newState ->
                    _uiState.value = newState
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load roadmap: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    fun updateNodeStatus(nodeId: String, newStatus: NodeStatus) {
        viewModelScope.launch {
            try {
                learningRepository.updateNodeStatus(nodeId, newStatus)
                
                // Update prerequisite unlocking logic
                if (newStatus == NodeStatus.COMPLETED) {
                    unlockDependentNodes(nodeId)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update node status: ${e.message}"
                )
            }
        }
    }
    
    private suspend fun unlockDependentNodes(completedNodeId: String) {
        val currentNodes = _uiState.value.nodesByStage.values.flatten()
        
        // Find nodes that have this completed node as a prerequisite
        val nodesToUnlock = currentNodes.filter { node ->
            node.status == NodeStatus.LOCKED && 
            node.prerequisiteNodeIds.contains(completedNodeId) &&
            areAllPrerequisitesCompleted(node, currentNodes)
        }
        
        // Unlock eligible nodes
        nodesToUnlock.forEach { node ->
            learningRepository.updateNodeStatus(node.id, NodeStatus.TODO)
        }
        
        // Check if we should unlock the next stage
        unlockNextStageIfReady()
    }
    
    private suspend fun areAllPrerequisitesCompleted(
        node: LearningNode, 
        allNodes: List<LearningNode>
    ): Boolean {
        return node.prerequisiteNodeIds.all { prereqId ->
            allNodes.find { it.id == prereqId }?.status == NodeStatus.COMPLETED
        }
    }
    
    private suspend fun unlockNextStageIfReady() {
        val currentState = _uiState.value
        
        currentState.stages.forEach { stage ->
            if (!stage.isUnlocked) {
                val stageNodes = currentState.nodesByStage[stage.id] ?: emptyList()
                val previousStageId = stage.id - 1
                val previousStageNodes = currentState.nodesByStage[previousStageId] ?: emptyList()
                
                // Unlock stage if all nodes in previous stage are completed
                if (previousStageNodes.isNotEmpty() && 
                    previousStageNodes.all { it.status == NodeStatus.COMPLETED }) {
                    
                    val unlockedStage = stage.copy(isUnlocked = true)
                    learningRepository.updateStage(unlockedStage)
                    
                    // Unlock the first node in the new stage
                    val firstNode = stageNodes.minByOrNull { it.order }
                    firstNode?.let {
                        learningRepository.updateNodeStatus(it.id, NodeStatus.TODO)
                    }
                }
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun getProgressPercentage(): Float {
        val allNodes = _uiState.value.nodesByStage.values.flatten()
        if (allNodes.isEmpty()) return 0f
        
        val completedNodes = allNodes.count { it.status == NodeStatus.COMPLETED }
        return (completedNodes.toFloat() / allNodes.size) * 100f
    }
    
    fun getStageProgress(stageId: Int): Float {
        val stageNodes = _uiState.value.nodesByStage[stageId] ?: emptyList()
        if (stageNodes.isEmpty()) return 0f
        
        val completedNodes = stageNodes.count { it.status == NodeStatus.COMPLETED }
        return (completedNodes.toFloat() / stageNodes.size) * 100f
    }
}
