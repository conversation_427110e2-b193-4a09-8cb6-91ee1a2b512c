<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="action_add_to_capstone">Add to Capstone</string>
    <string name="action_go_to_resource">Go to Resource</string>
    <string name="action_mark_complete">Mark Complete</string>
    <string name="action_mark_in_progress">Mark In Progress</string>
    <string name="app_name">Codex AI Journey Planner</string>
    <string name="back">Back</string>
    <string name="cancel">Cancel</string>
    <string name="capstone_hub_title">Capstone Hub</string>
    <string name="decisions_log_title">Decisions Log</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="node_detail_title">Node Details</string>
    <string name="prd_title">One-Page PRD</string>
    <string name="project_tasks_title">Project Tasks</string>
    <string name="roadmap_subtitle">Your AI Learning Journey</string>
    <string name="roadmap_title">Learning Roadmap</string>
    <string name="save">Save</string>
    <string name="stage_architect_engineer">The Architect &amp; Engineer</string>
    <string name="stage_foundations">The Twin Foundations</string>
    <string name="stage_genai_specialist">The GenAI Specialist</string>
    <string name="stage_ml_practitioner">The ML Practitioner</string>
    <string name="stage_product_visionary">The Product Visionary</string>
    <string name="status_completed">Completed</string>
    <string name="status_in_progress">In Progress</string>
    <string name="status_locked">Locked</string>
    <string name="status_todo">To Do</string>
    <style name="Base.Theme.CodexAIJourneyPlanner" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="Theme.CodexAIJourneyPlanner" parent="Base.Theme.CodexAIJourneyPlanner"/>
</resources>