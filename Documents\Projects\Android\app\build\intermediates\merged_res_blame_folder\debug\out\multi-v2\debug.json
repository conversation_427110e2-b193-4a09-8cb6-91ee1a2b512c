{"logs": [{"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1338,1435,5358,5452,5627,5801,5884,5993,6084,6179,6259,6338,6420,6590,6972,7052,7121", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "1430,1514,5447,5548,5713,5879,5988,6079,6174,6254,6333,6415,6501,6675,7047,7116,7236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,420,542,626,718,828,975,1102,1239,1319,1419,1521,1625,1753,1872,1977,2119,2259,2389,2591,2716,2833,2955,3098,3201,3295,3434,3566,3668,3777,3880,4021,4169,4274,4381,4455,4538,4622,4704,4813,4889,4973,5073,5189,5280,5381,5466,5584,5684,5786,5915,5991,6098", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "174,298,415,537,621,713,823,970,1097,1234,1314,1414,1516,1620,1748,1867,1972,2114,2254,2384,2586,2711,2828,2950,3093,3196,3290,3429,3561,3663,3772,3875,4016,4164,4269,4376,4450,4533,4617,4699,4808,4884,4968,5068,5184,5275,5376,5461,5579,5679,5781,5910,5986,6093,6191"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,229,353,470,1519,1603,1695,1805,1952,2079,2216,2296,2396,2498,2602,2730,2849,2954,3096,3236,3366,3568,3693,3810,3932,4075,4178,4272,4411,4543,4645,4754,4857,4998,5146,5251,5553,5718,6506,6680,6863,7241,7317,7401,7501,7617,7708,7809,7894,8012,8112,8214,8343,8419,8526", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "224,348,465,587,1598,1690,1800,1947,2074,2211,2291,2391,2493,2597,2725,2844,2949,3091,3231,3361,3563,3688,3805,3927,4070,4173,4267,4406,4538,4640,4749,4852,4993,5141,5246,5353,5622,5796,6585,6757,6967,7312,7396,7496,7612,7703,7804,7889,8007,8107,8209,8338,8414,8521,8619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "592,688,791,890,988,1095,1210,6762", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "683,786,885,983,1090,1205,1333,6858"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,389,501,578,676,792,934,1053,1205,1288,1398,1489,1584,1702,1820,1923,2059,2193,2322,2495,2620,2732,2848,2968,3060,3154,3273,3410,3512,3613,3717,3851,3991,4096,4193,4280,4358,4442,4523,4633,4709,4788,4883,4980,5067,5159,5241,5341,5435,5530,5643,5719,5820", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "162,273,384,496,573,671,787,929,1048,1200,1283,1393,1484,1579,1697,1815,1918,2054,2188,2317,2490,2615,2727,2843,2963,3055,3149,3268,3405,3507,3608,3712,3846,3986,4091,4188,4275,4353,4437,4518,4628,4704,4783,4878,4975,5062,5154,5236,5336,5430,5525,5638,5714,5815,5905"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,328,439,1452,1529,1627,1743,1885,2004,2156,2239,2349,2440,2535,2653,2771,2874,3010,3144,3273,3446,3571,3683,3799,3919,4011,4105,4224,4361,4463,4564,4668,4802,4942,5047,5338,5508,6247,6405,6587,6965,7041,7120,7215,7312,7399,7491,7573,7673,7767,7862,7975,8051,8152", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "212,323,434,546,1524,1622,1738,1880,1999,2151,2234,2344,2435,2530,2648,2766,2869,3005,3139,3268,3441,3566,3678,3794,3914,4006,4100,4219,4356,4458,4559,4663,4797,4937,5042,5139,5420,5581,6326,6481,6692,7036,7115,7210,7307,7394,7486,7568,7668,7762,7857,7970,8046,8147,8237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "551,651,756,854,953,1058,1160,6486", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "646,751,849,948,1053,1155,1266,6582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,980,1051,1136,1224,1298,1379,1448", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,975,1046,1131,1219,1293,1374,1443,1561"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1271,1370,5144,5238,5425,5586,5668,5754,5849,5931,6003,6074,6159,6331,6697,6778,6847", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "1365,1447,5233,5333,5503,5663,5749,5844,5926,5998,6069,6154,6242,6400,6773,6842,6960"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1361,1431", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1356,1426,1547"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1317,1412,5117,5213,5391,5554,5631,5718,5810,5892,5965,6037,6119,6289,6643,6721,6791", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "1407,1488,5208,5306,5471,5626,5713,5805,5887,5960,6032,6114,6200,6356,6716,6786,6907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,305,419,544,621,713,823,959,1081,1220,1301,1396,1485,1578,1691,1809,1909,2042,2172,2299,2479,2602,2721,2843,2962,3052,3146,3262,3382,3478,3583,3685,3823,3966,4071,4168,4248,4326,4410,4492,4591,4669,4748,4839,4935,5022,5115,5199,5299,5392,5489,5627,5705,5807", "endColumns": "124,124,113,124,76,91,109,135,121,138,80,94,88,92,112,117,99,132,129,126,179,122,118,121,118,89,93,115,119,95,104,101,137,142,104,96,79,77,83,81,98,77,78,90,95,86,92,83,99,92,96,137,77,101,94", "endOffsets": "175,300,414,539,616,708,818,954,1076,1215,1296,1391,1480,1573,1686,1804,1904,2037,2167,2294,2474,2597,2716,2838,2957,3047,3141,3257,3377,3473,3578,3680,3818,3961,4066,4163,4243,4321,4405,4487,4586,4664,4743,4834,4930,5017,5110,5194,5294,5387,5484,5622,5700,5802,5897"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,230,355,469,1493,1570,1662,1772,1908,2030,2169,2250,2345,2434,2527,2640,2758,2858,2991,3121,3248,3428,3551,3670,3792,3911,4001,4095,4211,4331,4427,4532,4634,4772,4915,5020,5311,5476,6205,6361,6544,6912,6990,7069,7160,7256,7343,7436,7520,7620,7713,7810,7948,8026,8128", "endColumns": "124,124,113,124,76,91,109,135,121,138,80,94,88,92,112,117,99,132,129,126,179,122,118,121,118,89,93,115,119,95,104,101,137,142,104,96,79,77,83,81,98,77,78,90,95,86,92,83,99,92,96,137,77,101,94", "endOffsets": "225,350,464,589,1565,1657,1767,1903,2025,2164,2245,2340,2429,2522,2635,2753,2853,2986,3116,3243,3423,3546,3665,3787,3906,3996,4090,4206,4326,4422,4527,4629,4767,4910,5015,5112,5386,5549,6284,6438,6638,6985,7064,7155,7251,7338,7431,7515,7615,7708,7805,7943,8021,8123,8218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "594,691,793,891,995,1098,1200,6443", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "686,788,886,990,1093,1195,1312,6539"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,337,449,1477,1553,1645,1755,1885,1999,2146,2226,2324,2415,2511,2622,2748,2851,2986,3120,3256,3418,3550,3666,3787,3911,4003,4096,4212,4324,4420,4527,4632,4768,4909,5015,5309,5477,6225,6386,6572,6925,7001,7081,7178,7280,7368,7463,7547,7655,7752,7851,7966,8042,8138", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "215,332,444,556,1548,1640,1750,1880,1994,2141,2221,2319,2410,2506,2617,2743,2846,2981,3115,3251,3413,3545,3661,3782,3906,3998,4091,4207,4319,4415,4522,4627,4763,4904,5010,5108,5386,5546,6305,6466,6664,6996,7076,7173,7275,7363,7458,7542,7650,7747,7846,7961,8037,8133,8221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1296,1391,5113,5210,5391,5551,5634,5731,5822,5909,5981,6050,6135,6310,6669,6745,6812", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "1386,1472,5205,5304,5472,5629,5726,5817,5904,5976,6045,6130,6220,6381,6740,6807,6920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,658,760,859,959,1066,1176,6471", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "653,755,854,954,1061,1171,1291,6567"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,644,746,843,944,1051,1158,6476", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "639,741,838,939,1046,1153,1268,6572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,390,500,578,674,788,924,1040,1188,1270,1366,1455,1553,1670,1793,1894,2025,2155,2298,2459,2608,2728,2854,2987,3077,3169,3287,3413,3506,3606,3712,3839,3976,4084,4179,4255,4334,4417,4498,4608,4689,4768,4863,4959,5057,5156,5240,5341,5437,5535,5656,5736,5850", "endColumns": "111,110,111,109,77,95,113,135,115,147,81,95,88,97,116,122,100,130,129,142,160,148,119,125,132,89,91,117,125,92,99,105,126,136,107,94,75,78,82,80,109,80,78,94,95,97,98,83,100,95,97,120,79,113,105", "endOffsets": "162,273,385,495,573,669,783,919,1035,1183,1265,1361,1450,1548,1665,1788,1889,2020,2150,2293,2454,2603,2723,2849,2982,3072,3164,3282,3408,3501,3601,3707,3834,3971,4079,4174,4250,4329,4412,4493,4603,4684,4763,4858,4954,5052,5151,5235,5336,5432,5530,5651,5731,5845,5951"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,328,440,1448,1526,1622,1736,1872,1988,2136,2218,2314,2403,2501,2618,2741,2842,2973,3103,3246,3407,3556,3676,3802,3935,4025,4117,4235,4361,4454,4554,4660,4787,4924,5032,5329,5491,6238,6395,6577,6955,7036,7115,7210,7306,7404,7503,7587,7688,7784,7882,8003,8083,8197", "endColumns": "111,110,111,109,77,95,113,135,115,147,81,95,88,97,116,122,100,130,129,142,160,148,119,125,132,89,91,117,125,92,99,105,126,136,107,94,75,78,82,80,109,80,78,94,95,97,98,83,100,95,97,120,79,113,105", "endOffsets": "212,323,435,545,1521,1617,1731,1867,1983,2131,2213,2309,2398,2496,2613,2736,2837,2968,3098,3241,3402,3551,3671,3797,3930,4020,4112,4230,4356,4449,4549,4655,4782,4919,5027,5122,5400,5565,6316,6471,6682,7031,7110,7205,7301,7399,7498,7582,7683,7779,7877,7998,8078,8192,8298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,993,1064,1149,1236,1310,1387,1457", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,988,1059,1144,1231,1305,1382,1452,1573"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1273,1367,5127,5228,5405,5570,5651,5752,5843,5925,5995,6066,6151,6321,6687,6764,6834", "endColumns": "93,80,100,100,85,80,100,90,81,69,70,84,86,73,76,69,120", "endOffsets": "1362,1443,5223,5324,5486,5646,5747,5838,5920,5990,6061,6146,6233,6390,6759,6829,6950"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,942,1008,1086,1168,1237,1311,1382", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,937,1003,1081,1163,1232,1306,1377,1496"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1252,1341,4901,4994,5166,5322,5399,5484,5570,5649,5715,5781,5859,6023,6373,6447,6518", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "1336,1420,4989,5084,5244,5394,5479,5565,5644,5710,5776,5854,5936,6087,6442,6513,6632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,394,505,579,670,780,914,1025,1161,1242,1336,1423,1515,1628,1744,1843,1975,2106,2223,2372,2486,2592,2704,2819,2907,3000,3108,3228,3322,3417,3517,3646,3783,3886,3981,4058,4131,4213,4294,4393,4469,4549,4646,4741,4828,4919,5001,5099,5194,5286,5407,5483,5580", "endColumns": "112,111,113,110,73,90,109,133,110,135,80,93,86,91,112,115,98,131,130,116,148,113,105,111,114,87,92,107,119,93,94,99,128,136,102,94,76,72,81,80,98,75,79,96,94,86,90,81,97,94,91,120,75,96,91", "endOffsets": "163,275,389,500,574,665,775,909,1020,1156,1237,1331,1418,1510,1623,1739,1838,1970,2101,2218,2367,2481,2587,2699,2814,2902,2995,3103,3223,3317,3412,3512,3641,3778,3881,3976,4053,4126,4208,4289,4388,4464,4544,4641,4736,4823,4914,4996,5094,5189,5281,5402,5478,5575,5667"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,330,444,1425,1499,1590,1700,1834,1945,2081,2162,2256,2343,2435,2548,2664,2763,2895,3026,3143,3292,3406,3512,3624,3739,3827,3920,4028,4148,4242,4337,4437,4566,4703,4806,5089,5249,5941,6092,6274,6637,6713,6793,6890,6985,7072,7163,7245,7343,7438,7530,7651,7727,7824", "endColumns": "112,111,113,110,73,90,109,133,110,135,80,93,86,91,112,115,98,131,130,116,148,113,105,111,114,87,92,107,119,93,94,99,128,136,102,94,76,72,81,80,98,75,79,96,94,86,90,81,97,94,91,120,75,96,91", "endOffsets": "213,325,439,550,1494,1585,1695,1829,1940,2076,2157,2251,2338,2430,2543,2659,2758,2890,3021,3138,3287,3401,3507,3619,3734,3822,3915,4023,4143,4237,4332,4432,4561,4698,4801,4896,5161,5317,6018,6168,6368,6708,6788,6885,6980,7067,7158,7240,7338,7433,7525,7646,7722,7819,7911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "555,649,751,848,945,1046,1146,6173", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "644,746,843,940,1041,1141,1247,6269"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,281,389,501,579,681,801,944,1063,1209,1292,1392,1481,1581,1695,1811,1916,2052,2186,2311,2485,2603,2719,2839,2959,3049,3146,3262,3391,3489,3592,3695,3832,3975,4080,4175,4247,4324,4409,4490,4585,4661,4740,4835,4930,5023,5119,5202,5301,5396,5495,5608,5684,5785", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "165,276,384,496,574,676,796,939,1058,1204,1287,1387,1476,1576,1690,1806,1911,2047,2181,2306,2480,2598,2714,2834,2954,3044,3141,3257,3386,3484,3587,3690,3827,3970,4075,4170,4242,4319,4404,4485,4580,4656,4735,4830,4925,5018,5114,5197,5296,5391,5490,5603,5679,5780,5871"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,331,439,1453,1531,1633,1753,1896,2015,2161,2244,2344,2433,2533,2647,2763,2868,3004,3138,3263,3437,3555,3671,3791,3911,4001,4098,4214,4343,4441,4544,4647,4784,4927,5032,5325,5483,6208,6365,6547,6905,6981,7060,7155,7250,7343,7439,7522,7621,7716,7815,7928,8004,8105", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "215,326,434,546,1526,1628,1748,1891,2010,2156,2239,2339,2428,2528,2642,2758,2863,2999,3133,3258,3432,3550,3666,3786,3906,3996,4093,4209,4338,4436,4539,4642,4779,4922,5027,5122,5392,5555,6288,6441,6637,6976,7055,7150,7245,7338,7434,7517,7616,7711,7810,7923,7999,8100,8191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "551,646,748,845,942,1048,1166,6446", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "641,743,840,937,1043,1161,1276,6542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1281,1371,5127,5225,5397,5560,5643,5734,5821,5906,5976,6043,6125,6293,6642,6720,6786", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "1366,1448,5220,5320,5478,5638,5729,5816,5901,5971,6038,6120,6203,6360,6715,6781,6900"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,609,711,812,913,1025,1103,1195,1275,1359,1460,1569,1666,1770,1872,1976,2088,2189,2287,2389,2492,2573,2664,2765,2870,2956,3054,3148,3252,3362,3458,3544,3613,3684,3762,3839,3924,4000,4078,4171,4261,4350,4439,4519,4611,4703,4793,4897,4973,5061", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "150,249,349,447,519,604,706,807,908,1020,1098,1190,1270,1354,1455,1564,1661,1765,1867,1971,2083,2184,2282,2384,2487,2568,2659,2760,2865,2951,3049,3143,3247,3357,3453,3539,3608,3679,3757,3834,3919,3995,4073,4166,4256,4345,4434,4514,4606,4698,4788,4892,4968,5056,5142"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,304,404,1313,1385,1470,1572,1673,1774,1886,1964,2056,2136,2220,2321,2430,2527,2631,2733,2837,2949,3050,3148,3250,3353,3434,3525,3626,3731,3817,3915,4009,4113,4223,4319,4584,4731,5379,5524,5702,6043,6119,6197,6290,6380,6469,6558,6638,6730,6822,6912,7016,7092,7180", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "200,299,399,497,1380,1465,1567,1668,1769,1881,1959,2051,2131,2215,2316,2425,2522,2626,2728,2832,2944,3045,3143,3245,3348,3429,3520,3621,3726,3812,3910,4004,4108,4218,4314,4400,4648,4797,5452,5596,5782,6114,6192,6285,6375,6464,6553,6633,6725,6817,6907,7011,7087,7175,7261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1163,1239,4405,4493,4653,4802,4876,4953,5031,5105,5168,5231,5304,5457,5787,5862,5927", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "1234,1308,4488,4579,4726,4871,4948,5026,5100,5163,5226,5299,5374,5519,5857,5922,6038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,594,693,787,881,974,1067,5601", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "589,688,782,876,969,1062,1158,5697"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "522,614,714,808,904,997,1090,5807", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "609,709,803,899,992,1085,1186,5903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,265,370,472,547,633,737,852,960,1080,1158,1251,1332,1418,1521,1630,1728,1848,1966,2076,2199,2305,2402,2503,2606,2691,2782,2886,2994,3081,3174,3267,3384,3507,3602,3689,3760,3834,3913,3992,4085,4161,4239,4333,4424,4513,4606,4685,4777,4868,4961,5068,5144,5240", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "157,260,365,467,542,628,732,847,955,1075,1153,1246,1327,1413,1516,1625,1723,1843,1961,2071,2194,2300,2397,2498,2601,2686,2777,2881,2989,3076,3169,3262,3379,3502,3597,3684,3755,3829,3908,3987,4080,4156,4234,4328,4419,4508,4601,4680,4772,4863,4956,5063,5139,5235,5325"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,315,420,1355,1430,1516,1620,1735,1843,1963,2041,2134,2215,2301,2404,2513,2611,2731,2849,2959,3082,3188,3285,3386,3489,3574,3665,3769,3877,3964,4057,4150,4267,4390,4485,4758,4912,5580,5728,5908,6259,6335,6413,6507,6598,6687,6780,6859,6951,7042,7135,7242,7318,7414", "endColumns": "106,102,104,101,74,85,103,114,107,119,77,92,80,85,102,108,97,119,117,109,122,105,96,100,102,84,90,103,107,86,92,92,116,122,94,86,70,73,78,78,92,75,77,93,90,88,92,78,91,90,92,106,75,95,89", "endOffsets": "207,310,415,517,1425,1511,1615,1730,1838,1958,2036,2129,2210,2296,2399,2508,2606,2726,2844,2954,3077,3183,3280,3381,3484,3569,3660,3764,3872,3959,4052,4145,4262,4385,4480,4567,4824,4981,5654,5802,5996,6330,6408,6502,6593,6682,6775,6854,6946,7037,7130,7237,7313,7409,7499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1191,1277,4572,4661,4829,4986,5064,5142,5227,5302,5366,5430,5504,5659,6001,6077,6142", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "1272,1350,4656,4753,4907,5059,5137,5222,5297,5361,5425,5499,5575,5723,6072,6137,6254"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "571,669,776,873,972,1076,1180,6465", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "664,771,868,967,1071,1175,1292,6561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,404,521,596,686,794,931,1046,1187,1268,1364,1455,1549,1664,1786,1887,2019,2150,2280,2444,2566,2686,2811,2932,3024,3118,3244,3374,3467,3565,3670,3806,3949,4054,4149,4230,4307,4397,4479,4584,4668,4747,4840,4937,5026,5125,5209,5310,5403,5499,5633,5719,5815", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "166,283,399,516,591,681,789,926,1041,1182,1263,1359,1450,1544,1659,1781,1882,2014,2145,2275,2439,2561,2681,2806,2927,3019,3113,3239,3369,3462,3560,3665,3801,3944,4049,4144,4225,4302,4392,4474,4579,4663,4742,4835,4932,5021,5120,5204,5305,5398,5494,5628,5714,5810,5898"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,338,454,1489,1564,1654,1762,1899,2014,2155,2236,2332,2423,2517,2632,2754,2855,2987,3118,3248,3412,3534,3654,3779,3900,3992,4086,4212,4342,4435,4533,4638,4774,4917,5022,5310,5481,6220,6383,6566,6938,7022,7101,7194,7291,7380,7479,7563,7664,7757,7853,7987,8073,8169", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "216,333,449,566,1559,1649,1757,1894,2009,2150,2231,2327,2418,2512,2627,2749,2850,2982,3113,3243,3407,3529,3649,3774,3895,3987,4081,4207,4337,4430,4528,4633,4769,4912,5017,5112,5386,5553,6305,6460,6666,7017,7096,7189,7286,7375,7474,7558,7659,7752,7848,7982,8068,8164,8252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1297,1402,5117,5211,5391,5558,5637,5730,5825,5910,5982,6053,6134,6310,6671,6750,6820", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "1397,1484,5206,5305,5476,5632,5725,5820,5905,5977,6048,6129,6215,6378,6745,6815,6933"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-or_values-or.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "560,663,765,868,973,1074,1176,6524", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "658,760,863,968,1069,1171,1290,6620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,395,510,596,692,806,945,1067,1214,1295,1396,1488,1579,1691,1817,1923,2062,2196,2324,2513,2636,2761,2893,3018,3111,3203,3316,3434,3535,3636,3735,3872,4018,4121,4225,4296,4380,4470,4557,4658,4734,4815,4912,5014,5103,5200,5283,5387,5482,5580,5700,5776,5875", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "168,281,390,505,591,687,801,940,1062,1209,1290,1391,1483,1574,1686,1812,1918,2057,2191,2319,2508,2631,2756,2888,3013,3106,3198,3311,3429,3530,3631,3730,3867,4013,4116,4220,4291,4375,4465,4552,4653,4729,4810,4907,5009,5098,5195,5278,5382,5477,5575,5695,5771,5870,5960"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,336,445,1479,1565,1661,1775,1914,2036,2183,2264,2365,2457,2548,2660,2786,2892,3031,3165,3293,3482,3605,3730,3862,3987,4080,4172,4285,4403,4504,4605,4704,4841,4987,5090,5386,5543,6277,6437,6625,6995,7071,7152,7249,7351,7440,7537,7620,7724,7819,7917,8037,8113,8212", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "218,331,440,555,1560,1656,1770,1909,2031,2178,2259,2360,2452,2543,2655,2781,2887,3026,3160,3288,3477,3600,3725,3857,3982,4075,4167,4280,4398,4499,4600,4699,4836,4982,5085,5189,5452,5622,6362,6519,6721,7066,7147,7244,7346,7435,7532,7615,7719,7814,7912,8032,8108,8207,8297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1295,1392,5194,5286,5457,5627,5704,5802,5890,5977,6047,6117,6195,6367,6726,6809,6876", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "1387,1474,5281,5381,5538,5699,5797,5885,5972,6042,6112,6190,6272,6432,6804,6871,6990"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1242,1332,4864,4973,5147,5314,5403,5493,5579,5662,5727,5793,5873,6039,6396,6474,6540", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "1327,1404,4968,5066,5231,5398,5488,5574,5657,5722,5788,5868,5952,6108,6469,6535,6656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,379,486,563,655,765,890,1004,1131,1212,1308,1394,1490,1604,1720,1821,1944,2065,2191,2337,2454,2564,2679,2788,2875,2970,3079,3200,3290,3389,3491,3613,3743,3849,3941,4017,4095,4177,4259,4359,4441,4524,4623,4721,4812,4911,4993,5090,5184,5281,5404,5486,5582", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "160,268,374,481,558,650,760,885,999,1126,1207,1303,1389,1485,1599,1715,1816,1939,2060,2186,2332,2449,2559,2674,2783,2870,2965,3074,3195,3285,3384,3486,3608,3738,3844,3936,4012,4090,4172,4254,4354,4436,4519,4618,4716,4807,4906,4988,5085,5179,5276,5399,5481,5577,5668"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,323,429,1409,1486,1578,1688,1813,1927,2054,2135,2231,2317,2413,2527,2643,2744,2867,2988,3114,3260,3377,3487,3602,3711,3798,3893,4002,4123,4213,4312,4414,4536,4666,4772,5071,5236,5957,6113,6296,6661,6743,6826,6925,7023,7114,7213,7295,7392,7486,7583,7706,7788,7884", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "210,318,424,531,1481,1573,1683,1808,1922,2049,2130,2226,2312,2408,2522,2638,2739,2862,2983,3109,3255,3372,3482,3597,3706,3793,3888,3997,4118,4208,4307,4409,4531,4661,4767,4859,5142,5309,6034,6190,6391,6738,6821,6920,7018,7109,7208,7290,7387,7481,7578,7701,7783,7879,7970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "536,632,735,834,932,1033,1131,6195", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "627,730,829,927,1028,1126,1237,6291"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-v16_values-v16.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,272,381,489,561,650,757,883,1003,1140,1222,1318,1405,1499,1613,1725,1826,1949,2069,2193,2341,2460,2574,2695,2813,2901,2996,3104,3233,3325,3439,3542,3663,3793,3899,3992,4069,4141,4223,4302,4403,4479,4562,4661,4759,4853,4953,5035,5132,5226,5324,5437,5513,5619", "endColumns": "108,107,108,107,71,88,106,125,119,136,81,95,86,93,113,111,100,122,119,123,147,118,113,120,117,87,94,107,128,91,113,102,120,129,105,92,76,71,81,78,100,75,82,98,97,93,99,81,96,93,97,112,75,105,101", "endOffsets": "159,267,376,484,556,645,752,878,998,1135,1217,1313,1400,1494,1608,1720,1821,1944,2064,2188,2336,2455,2569,2690,2808,2896,2991,3099,3228,3320,3434,3537,3658,3788,3894,3987,4064,4136,4218,4297,4398,4474,4557,4656,4754,4848,4948,5030,5127,5221,5319,5432,5508,5614,5716"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,322,431,1417,1489,1578,1685,1811,1931,2068,2150,2246,2333,2427,2541,2653,2754,2877,2997,3121,3269,3388,3502,3623,3741,3829,3924,4032,4161,4253,4367,4470,4591,4721,4827,5118,5283,6000,6155,6335,6704,6780,6863,6962,7060,7154,7254,7336,7433,7527,7625,7738,7814,7920", "endColumns": "108,107,108,107,71,88,106,125,119,136,81,95,86,93,113,111,100,122,119,123,147,118,113,120,117,87,94,107,128,91,113,102,120,129,105,92,76,71,81,78,100,75,82,98,97,93,99,81,96,93,97,112,75,105,101", "endOffsets": "209,317,426,534,1484,1573,1680,1806,1926,2063,2145,2241,2328,2422,2536,2648,2749,2872,2992,3116,3264,3383,3497,3618,3736,3824,3919,4027,4156,4248,4362,4465,4586,4716,4822,4915,5190,5350,6077,6229,6431,6775,6858,6957,7055,7149,7249,7331,7428,7522,7620,7733,7809,7915,8017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,1340,4920,5017,5195,5355,5440,5525,5611,5694,5759,5825,5911,6082,6436,6514,6581", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "1335,1412,5012,5113,5278,5435,5520,5606,5689,5754,5820,5906,5995,6150,6509,6576,6699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "539,635,738,836,934,1037,1142,6234", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "630,733,831,929,1032,1137,1249,6330"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-as_values-as.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "559,660,763,871,976,1080,1180,6460", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "655,758,866,971,1075,1175,1304,6556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1006,1078,1161,1246,1325,1400,1466", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1001,1073,1156,1241,1320,1395,1461,1579"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1309,1404,5089,5182,5353,5523,5620,5719,5808,5898,5966,6038,6121,6293,6666,6741,6807", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "1399,1484,5177,5275,5435,5615,5714,5803,5893,5961,6033,6116,6201,6367,6736,6802,6920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,395,509,592,682,790,930,1047,1185,1266,1364,1455,1550,1662,1786,1889,2016,2142,2267,2443,2559,2673,2794,2909,3001,3093,3210,3332,3425,3531,3635,3766,3906,4012,4109,4182,4265,4352,4440,4545,4623,4704,4799,4900,4994,5092,5175,5278,5371,5470,5599,5679,5778", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "165,281,390,504,587,677,785,925,1042,1180,1261,1359,1450,1545,1657,1781,1884,2011,2137,2262,2438,2554,2668,2789,2904,2996,3088,3205,3327,3420,3526,3630,3761,3901,4007,4104,4177,4260,4347,4435,4540,4618,4699,4794,4895,4989,5087,5170,5273,5366,5465,5594,5674,5773,5863"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,336,445,1489,1572,1662,1770,1910,2027,2165,2246,2344,2435,2530,2642,2766,2869,2996,3122,3247,3423,3539,3653,3774,3889,3981,4073,4190,4312,4405,4511,4615,4746,4886,4992,5280,5440,6206,6372,6561,6925,7003,7084,7179,7280,7374,7472,7555,7658,7751,7850,7979,8059,8158", "endColumns": "114,115,108,113,82,89,107,139,116,137,80,97,90,94,111,123,102,126,125,124,175,115,113,120,114,91,91,116,121,92,105,103,130,139,105,96,72,82,86,87,104,77,80,94,100,93,97,82,102,92,98,128,79,98,89", "endOffsets": "215,331,440,554,1567,1657,1765,1905,2022,2160,2241,2339,2430,2525,2637,2761,2864,2991,3117,3242,3418,3534,3648,3769,3884,3976,4068,4185,4307,4400,4506,4610,4741,4881,4987,5084,5348,5518,6288,6455,6661,6998,7079,7174,7275,7369,7467,7550,7653,7746,7845,7974,8054,8153,8243"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "577,678,780,883,987,1088,1193,6423", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "673,775,878,982,1083,1188,1299,6519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,994,1061,1142,1231,1303,1384,1450", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,989,1056,1137,1226,1298,1379,1445,1562"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1304,1404,5091,5188,5361,5535,5616,5704,5796,5878,5947,6014,6095,6268,6625,6706,6772", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "1399,1487,5183,5284,5447,5611,5699,5791,5873,5942,6009,6090,6179,6335,6701,6767,6884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,297,406,527,612,710,826,958,1075,1217,1298,1397,1484,1578,1686,1804,1908,2043,2176,2302,2462,2583,2694,2809,2922,3010,3105,3218,3346,3448,3549,3651,3784,3923,4029,4126,4198,4281,4365,4448,4549,4625,4705,4801,4898,4991,5085,5169,5269,5365,5462,5583,5659,5757", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "174,292,401,522,607,705,821,953,1070,1212,1293,1392,1479,1573,1681,1799,1903,2038,2171,2297,2457,2578,2689,2804,2917,3005,3100,3213,3341,3443,3544,3646,3779,3918,4024,4121,4193,4276,4360,4443,4544,4620,4700,4796,4893,4986,5080,5164,5264,5360,5457,5578,5654,5752,5846"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,229,347,456,1492,1577,1675,1791,1923,2040,2182,2263,2362,2449,2543,2651,2769,2873,3008,3141,3267,3427,3548,3659,3774,3887,3975,4070,4183,4311,4413,4514,4616,4749,4888,4994,5289,5452,6184,6340,6524,6889,6965,7045,7141,7238,7331,7425,7509,7609,7705,7802,7923,7999,8097", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "224,342,451,572,1572,1670,1786,1918,2035,2177,2258,2357,2444,2538,2646,2764,2868,3003,3136,3262,3422,3543,3654,3769,3882,3970,4065,4178,4306,4408,4509,4611,4744,4883,4989,5086,5356,5530,6263,6418,6620,6960,7040,7136,7233,7326,7420,7504,7604,7700,7797,7918,7994,8092,8186"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,670,772,871,971,1078,1184,6565", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "665,767,866,966,1073,1179,1300,6661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,407,523,600,695,808,945,1059,1200,1280,1378,1469,1565,1676,1799,1902,2043,2183,2325,2513,2647,2764,2885,3007,3099,3192,3313,3447,3547,3654,3755,3896,4043,4149,4249,4331,4408,4494,4577,4674,4750,4830,4927,5029,5118,5214,5298,5406,5503,5603,5718,5794,5894", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "167,283,402,518,595,690,803,940,1054,1195,1275,1373,1464,1560,1671,1794,1897,2038,2178,2320,2508,2642,2759,2880,3002,3094,3187,3308,3442,3542,3649,3750,3891,4038,4144,4244,4326,4403,4489,4572,4669,4745,4825,4922,5024,5113,5209,5293,5401,5498,5598,5713,5789,5889,5981"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,338,457,1483,1560,1655,1768,1905,2019,2160,2240,2338,2429,2525,2636,2759,2862,3003,3143,3285,3473,3607,3724,3845,3967,4059,4152,4273,4407,4507,4614,4715,4856,5003,5109,5405,5573,6320,6482,6666,7033,7109,7189,7286,7388,7477,7573,7657,7765,7862,7962,8077,8153,8253", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "217,333,452,568,1555,1650,1763,1900,2014,2155,2235,2333,2424,2520,2631,2754,2857,2998,3138,3280,3468,3602,3719,3840,3962,4054,4147,4268,4402,4502,4609,4710,4851,4998,5104,5204,5482,5645,6401,6560,6758,7104,7184,7281,7383,7472,7568,7652,7760,7857,7957,8072,8148,8248,8340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1305,1400,5209,5306,5487,5650,5729,5826,5917,6004,6076,6145,6230,6406,6763,6839,6911", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "1395,1478,5301,5400,5568,5724,5821,5912,5999,6071,6140,6225,6315,6477,6834,6906,7028"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "569,667,769,867,971,1075,1177,6459", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "662,764,862,966,1070,1172,1289,6555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,991,1062,1143,1229,1302,1382,1452", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,986,1057,1138,1224,1297,1377,1447,1565"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1294,1395,5116,5210,5390,5553,5630,5722,5814,5899,5971,6042,6123,6299,6665,6745,6815", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "1390,1478,5205,5304,5471,5625,5717,5809,5894,5966,6037,6118,6204,6367,6740,6810,6928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,403,519,594,688,800,941,1057,1191,1272,1368,1459,1553,1666,1789,1890,2021,2151,2288,2454,2585,2705,2830,2950,3040,3134,3251,3375,3471,3569,3674,3808,3949,4054,4152,4233,4310,4400,4487,4592,4671,4750,4843,4943,5032,5125,5209,5313,5406,5503,5637,5723,5826", "endColumns": "114,115,116,115,74,93,111,140,115,133,80,95,90,93,112,122,100,130,129,136,165,130,119,124,119,89,93,116,123,95,97,104,133,140,104,97,80,76,89,86,104,78,78,92,99,88,92,83,103,92,96,133,85,102,94", "endOffsets": "165,281,398,514,589,683,795,936,1052,1186,1267,1363,1454,1548,1661,1784,1885,2016,2146,2283,2449,2580,2700,2825,2945,3035,3129,3246,3370,3466,3564,3669,3803,3944,4049,4147,4228,4305,4395,4482,4587,4666,4745,4838,4938,5027,5120,5204,5308,5401,5498,5632,5718,5821,5916"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,336,453,1483,1558,1652,1764,1905,2021,2155,2236,2332,2423,2517,2630,2753,2854,2985,3115,3252,3418,3549,3669,3794,3914,4004,4098,4215,4339,4435,4533,4638,4772,4913,5018,5309,5476,6209,6372,6560,6933,7012,7091,7184,7284,7373,7466,7550,7654,7747,7844,7978,8064,8167", "endColumns": "114,115,116,115,74,93,111,140,115,133,80,95,90,93,112,122,100,130,129,136,165,130,119,124,119,89,93,116,123,95,97,104,133,140,104,97,80,76,89,86,104,78,78,92,99,88,92,83,103,92,96,133,85,102,94", "endOffsets": "215,331,448,564,1553,1647,1759,1900,2016,2150,2231,2327,2418,2512,2625,2748,2849,2980,3110,3247,3413,3544,3664,3789,3909,3999,4093,4210,4334,4430,4528,4633,4767,4908,5013,5111,5385,5548,6294,6454,6660,7007,7086,7179,7279,7368,7461,7545,7649,7742,7839,7973,8059,8162,8257"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,389,499,577,671,783,912,1017,1152,1232,1327,1417,1511,1621,1738,1843,1964,2083,2209,2373,2494,2611,2732,2850,2941,3035,3148,3270,3370,3476,3579,3697,3821,3930,4029,4109,4185,4269,4351,4448,4524,4604,4700,4800,4892,4987,5071,5175,5271,5369,5504,5580,5692", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "163,274,384,494,572,666,778,907,1012,1147,1227,1322,1412,1506,1616,1733,1838,1959,2078,2204,2368,2489,2606,2727,2845,2936,3030,3143,3265,3365,3471,3574,3692,3816,3925,4024,4104,4180,4264,4346,4443,4519,4599,4695,4795,4887,4982,5066,5170,5266,5364,5499,5575,5687,5786"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,329,439,1469,1547,1641,1753,1882,1987,2122,2202,2297,2387,2481,2591,2708,2813,2934,3053,3179,3343,3464,3581,3702,3820,3911,4005,4118,4240,4340,4446,4549,4667,4791,4900,5210,5386,6110,6268,6451,6816,6892,6972,7068,7168,7260,7355,7439,7543,7639,7737,7872,7948,8060", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "213,324,434,544,1542,1636,1748,1877,1982,2117,2197,2292,2382,2476,2586,2703,2808,2929,3048,3174,3338,3459,3576,3697,3815,3906,4000,4113,4235,4335,4441,4544,4662,4786,4895,4994,5285,5457,6189,6345,6543,6887,6967,7063,7163,7255,7350,7434,7538,7634,7732,7867,7943,8055,8154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "549,651,753,854,954,1062,1166,6350", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "646,748,849,949,1057,1161,1280,6446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1285,1384,4999,5103,5290,5462,5545,5635,5728,5811,5879,5946,6027,6194,6548,6631,6699", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "1379,1464,5098,5205,5381,5540,5630,5723,5806,5874,5941,6022,6105,6263,6626,6694,6811"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "541,635,737,834,933,1041,1147,6272", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "630,732,829,928,1036,1142,1262,6368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,382,491,566,656,764,904,1022,1168,1248,1344,1429,1519,1622,1736,1837,1960,2078,2207,2370,2493,2606,2725,2843,2929,3023,3138,3271,3368,3474,3573,3702,3839,3940,4033,4109,4182,4262,4342,4449,4525,4605,4702,4797,4884,4980,5064,5165,5263,5363,5476,5552,5651", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "163,274,377,486,561,651,759,899,1017,1163,1243,1339,1424,1514,1617,1731,1832,1955,2073,2202,2365,2488,2601,2720,2838,2924,3018,3133,3266,3363,3469,3568,3697,3834,3935,4028,4104,4177,4257,4337,4444,4520,4600,4697,4792,4879,4975,5059,5160,5258,5358,5471,5547,5646,5741"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,329,432,1441,1516,1606,1714,1854,1972,2118,2198,2294,2379,2469,2572,2686,2787,2910,3028,3157,3320,3443,3556,3675,3793,3879,3973,4088,4221,4318,4424,4523,4652,4789,4890,5180,5344,6042,6192,6373,6744,6820,6900,6997,7092,7179,7275,7359,7460,7558,7658,7771,7847,7946", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "213,324,427,536,1511,1601,1709,1849,1967,2113,2193,2289,2374,2464,2567,2681,2782,2905,3023,3152,3315,3438,3551,3670,3788,3874,3968,4083,4216,4313,4419,4518,4647,4784,4885,4978,5251,5412,6117,6267,6475,6815,6895,6992,7087,7174,7270,7354,7455,7553,7653,7766,7842,7941,8036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1267,1360,4983,5080,5256,5417,5493,5581,5670,5752,5816,5880,5960,6122,6480,6557,6624", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "1355,1436,5075,5175,5339,5488,5576,5665,5747,5811,5875,5955,6037,6187,6552,6619,6739"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,410,526,604,698,810,949,1063,1211,1292,1390,1483,1581,1695,1814,1914,2046,2175,2310,2482,2609,2725,2844,2968,3062,3154,3271,3400,3497,3598,3709,3839,3976,4083,4183,4256,4333,4416,4501,4608,4686,4766,4863,4965,5061,5156,5240,5351,5448,5547,5666,5744,5847", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "168,286,405,521,599,693,805,944,1058,1206,1287,1385,1478,1576,1690,1809,1909,2041,2170,2305,2477,2604,2720,2839,2963,3057,3149,3266,3395,3492,3593,3704,3834,3971,4078,4178,4251,4328,4411,4496,4603,4681,4761,4858,4960,5056,5151,5235,5346,5443,5542,5661,5739,5842,5937"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,341,460,1489,1567,1661,1773,1912,2026,2174,2255,2353,2446,2544,2658,2777,2877,3009,3138,3273,3445,3572,3688,3807,3931,4025,4117,4234,4363,4460,4561,4672,4802,4939,5046,5347,5509,6245,6405,6591,6969,7047,7127,7224,7326,7422,7517,7601,7712,7809,7908,8027,8105,8208", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "218,336,455,571,1562,1656,1768,1907,2021,2169,2250,2348,2441,2539,2653,2772,2872,3004,3133,3268,3440,3567,3683,3802,3926,4020,4112,4229,4358,4455,4556,4667,4797,4934,5041,5141,5415,5581,6323,6485,6693,7042,7122,7219,7321,7417,7512,7596,7707,7804,7903,8022,8100,8203,8298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "576,675,777,877,975,1082,1188,6490", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "670,772,872,970,1077,1183,1303,6586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1308,1407,5146,5244,5420,5586,5665,5761,5853,5940,6004,6068,6155,6328,6698,6776,6846", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "1402,1484,5239,5342,5504,5660,5756,5848,5935,5999,6063,6150,6240,6400,6771,6841,6964"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,381,491,571,668,783,916,1034,1182,1268,1360,1454,1552,1666,1785,1882,2010,2138,2263,2426,2544,2664,2788,2907,3002,3097,3214,3332,3431,3538,3642,3776,3916,4020,4121,4200,4279,4359,4441,4537,4613,4694,4787,4886,4979,5077,5163,5267,5366,5469,5583,5659,5760", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "162,271,376,486,566,663,778,911,1029,1177,1263,1355,1449,1547,1661,1780,1877,2005,2133,2258,2421,2539,2659,2783,2902,2997,3092,3209,3327,3426,3533,3637,3771,3911,4015,4116,4195,4274,4354,4436,4532,4608,4689,4782,4881,4974,5072,5158,5262,5361,5464,5578,5654,5755,5850"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,326,431,1454,1534,1631,1746,1879,1997,2145,2231,2323,2417,2515,2629,2748,2845,2973,3101,3226,3389,3507,3627,3751,3870,3965,4060,4177,4295,4394,4501,4605,4739,4879,4983,5284,5452,6171,6323,6506,6873,6949,7030,7123,7222,7315,7413,7499,7603,7702,7805,7919,7995,8096", "endColumns": "111,108,104,109,79,96,114,132,117,147,85,91,93,97,113,118,96,127,127,124,162,117,119,123,118,94,94,116,117,98,106,103,133,139,103,100,78,78,79,81,95,75,80,92,98,92,97,85,103,98,102,113,75,100,94", "endOffsets": "212,321,426,536,1529,1626,1741,1874,1992,2140,2226,2318,2412,2510,2624,2743,2840,2968,3096,3221,3384,3502,3622,3746,3865,3960,4055,4172,4290,4389,4496,4600,4734,4874,4978,5079,5358,5526,6246,6400,6597,6944,7025,7118,7217,7310,7408,7494,7598,7697,7800,7914,7990,8091,8186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "541,637,739,837,942,1047,1159,6405", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "632,734,832,937,1042,1154,1270,6501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1275,1369,5084,5183,5363,5531,5608,5701,5792,5874,5940,6008,6089,6251,6602,6679,6751", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "1364,1449,5178,5279,5447,5603,5696,5787,5869,5935,6003,6084,6166,6318,6674,6746,6868"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1314,1412,5152,5248,5430,5596,5682,5770,5863,5947,6017,6087,6172,6345,6710,6788,6856", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "1407,1496,5243,5346,5515,5677,5765,5858,5942,6012,6082,6167,6254,6413,6783,6851,6973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "583,681,783,883,984,1091,1199,6502", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "676,778,878,979,1086,1194,1309,6598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,301,412,533,610,706,820,954,1068,1211,1293,1387,1477,1574,1689,1809,1908,2042,2169,2303,2482,2610,2725,2849,2966,3057,3152,3269,3400,3498,3608,3710,3840,3981,4086,4184,4263,4339,4425,4509,4616,4692,4775,4866,4966,5053,5149,5234,5337,5435,5532,5681,5757,5858", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "173,296,407,528,605,701,815,949,1063,1206,1288,1382,1472,1569,1684,1804,1903,2037,2164,2298,2477,2605,2720,2844,2961,3052,3147,3264,3395,3493,3603,3705,3835,3976,4081,4179,4258,4334,4420,4504,4611,4687,4770,4861,4961,5048,5144,5229,5332,5430,5527,5676,5752,5853,5948"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,351,462,1501,1578,1674,1788,1922,2036,2179,2261,2355,2445,2542,2657,2777,2876,3010,3137,3271,3450,3578,3693,3817,3934,4025,4120,4237,4368,4466,4576,4678,4808,4949,5054,5351,5520,6259,6418,6603,6978,7054,7137,7228,7328,7415,7511,7596,7699,7797,7894,8043,8119,8220", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "223,346,457,578,1573,1669,1783,1917,2031,2174,2256,2350,2440,2537,2652,2772,2871,3005,3132,3266,3445,3573,3688,3812,3929,4020,4115,4232,4363,4461,4571,4673,4803,4944,5049,5147,5425,5591,6340,6497,6705,7049,7132,7223,7323,7410,7506,7591,7694,7792,7889,8038,8114,8215,8310"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,644,746,845,944,1048,1151,6243", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "639,741,840,939,1043,1146,1262,6339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1267,1359,4941,5035,5208,5371,5453,5542,5631,5715,5780,5844,5922,6087,6442,6519,6585", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "1354,1436,5030,5129,5290,5448,5537,5626,5710,5775,5839,5917,5999,6155,6514,6580,6701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4072,4148,4231,4314,4412,4488,4568,4665,4762,4858,4953,5037,5139,5236,5335,5451,5527,5623", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4067,4143,4226,4309,4407,4483,4563,4660,4757,4853,4948,5032,5134,5231,5330,5446,5522,5618,5709"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,438,1441,1519,1610,1719,1851,1963,2095,2175,2270,2357,2450,2565,2686,2786,2909,3028,3152,3310,3427,3539,3659,3781,3869,3963,4076,4196,4289,4387,4485,4610,4745,4847,5134,5295,6004,6160,6344,6706,6782,6862,6959,7056,7152,7247,7331,7433,7530,7629,7745,7821,7917", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "214,326,433,543,1514,1605,1714,1846,1958,2090,2170,2265,2352,2445,2560,2681,2781,2904,3023,3147,3305,3422,3534,3654,3776,3864,3958,4071,4191,4284,4382,4480,4605,4740,4842,4936,5203,5366,6082,6238,6437,6777,6857,6954,7051,7147,7242,7326,7428,7525,7624,7740,7816,7912,8003"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4072,4148,4231,4314,4412,4488,4568,4665,4762,4858,4953,5037,5139,5236,5335,5451,5527,5623", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4067,4143,4226,4309,4407,4483,4563,4660,4757,4853,4948,5032,5134,5231,5330,5446,5522,5618,5709"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,438,1441,1519,1610,1719,1851,1963,2095,2175,2270,2357,2450,2565,2686,2786,2909,3028,3152,3310,3427,3539,3659,3781,3869,3963,4076,4196,4289,4387,4485,4610,4745,4847,5134,5295,6004,6160,6344,6706,6782,6862,6959,7056,7152,7247,7331,7433,7530,7629,7745,7821,7917", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "214,326,433,543,1514,1605,1714,1846,1958,2090,2170,2265,2352,2445,2560,2681,2781,2904,3023,3147,3305,3422,3534,3654,3776,3864,3958,4071,4191,4284,4382,4480,4605,4740,4842,4936,5203,5366,6082,6238,6437,6777,6857,6954,7051,7147,7242,7326,7428,7525,7624,7740,7816,7912,8003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,644,746,845,944,1048,1151,6243", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "639,741,840,939,1043,1146,1262,6339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1267,1359,4941,5035,5208,5371,5453,5542,5631,5715,5780,5844,5922,6087,6442,6519,6585", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "1354,1436,5030,5129,5290,5448,5537,5626,5710,5775,5839,5917,5999,6155,6514,6580,6701"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2384,2575,9530,9727,10103,10469,10654,10847,11035,11222,11387,11554,11735,12106,12862,13041,13208", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "2570,2755,9722,9924,10285,10649,10842,11030,11217,11382,11549,11730,11915,12267,13036,13203,13441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,272,485,695,907,1088,1281,1493,1728,1943,2178,2361,2559,2748,2943,3161,3385,3588,3814,4035,4262,4518,4736,4951,5174,5398,5589,5786,6002,6223,6418,6619,6820,7041,7278,7483,7677,7851,8030,8216,8401,8602,8781,8964,9163,9363,9562,9760,9947,10152,10351,10550,10765,10942,11140", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "267,480,690,902,1083,1276,1488,1723,1938,2173,2356,2554,2743,2938,3156,3380,3583,3809,4030,4257,4513,4731,4946,5169,5393,5584,5781,5997,6218,6413,6614,6815,7036,7273,7478,7672,7846,8025,8211,8396,8597,8776,8959,9158,9358,9557,9755,9942,10147,10346,10545,10760,10937,11135,11329"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,322,535,745,2760,2941,3134,3346,3581,3796,4031,4214,4412,4601,4796,5014,5238,5441,5667,5888,6115,6371,6589,6804,7027,7251,7442,7639,7855,8076,8271,8472,8673,8894,9131,9336,9929,10290,11920,12272,12661,13446,13625,13808,14007,14207,14406,14604,14791,14996,15195,15394,15609,15786,15984", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "317,530,740,952,2936,3129,3341,3576,3791,4026,4209,4407,4596,4791,5009,5233,5436,5662,5883,6110,6366,6584,6799,7022,7246,7437,7634,7850,8071,8266,8467,8668,8889,9126,9331,9525,10098,10464,12101,12452,12857,13620,13803,14002,14202,14401,14599,14786,14991,15190,15389,15604,15781,15979,16173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "957,1153,1358,1559,1760,1967,2172,12457", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "1148,1353,1554,1755,1962,2167,2379,12656"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "582,680,783,884,990,1091,1199,6596", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "675,778,879,985,1086,1194,1322,6692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1327,1424,5247,5343,5515,5686,5770,5863,5954,6039,6110,6181,6263,6432,6804,6881,6950", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "1419,1503,5338,5438,5599,5765,5858,5949,6034,6105,6176,6258,6344,6506,6876,6945,7063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,413,532,614,708,820,973,1099,1247,1329,1431,1528,1634,1746,1875,1982,2117,2248,2377,2561,2681,2795,2913,3037,3135,3228,3346,3480,3582,3687,3789,3923,4064,4167,4271,4343,4425,4508,4593,4700,4776,4856,4952,5056,5152,5249,5332,5441,5539,5639,5756,5832,5938", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "170,290,408,527,609,703,815,968,1094,1242,1324,1426,1523,1629,1741,1870,1977,2112,2243,2372,2556,2676,2790,2908,3032,3130,3223,3341,3475,3577,3682,3784,3918,4059,4162,4266,4338,4420,4503,4588,4695,4771,4851,4947,5051,5147,5244,5327,5436,5534,5634,5751,5827,5933,6026"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,345,463,1508,1590,1684,1796,1949,2075,2223,2305,2407,2504,2610,2722,2851,2958,3093,3224,3353,3537,3657,3771,3889,4013,4111,4204,4322,4456,4558,4663,4765,4899,5040,5143,5443,5604,6349,6511,6697,7068,7144,7224,7320,7424,7520,7617,7700,7809,7907,8007,8124,8200,8306", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "220,340,458,577,1585,1679,1791,1944,2070,2218,2300,2402,2499,2605,2717,2846,2953,3088,3219,3348,3532,3652,3766,3884,4008,4106,4199,4317,4451,4553,4658,4760,4894,5035,5138,5242,5510,5681,6427,6591,6799,7139,7219,7315,7419,7515,7612,7695,7804,7902,8002,8119,8195,8301,8394"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,974,1042,1120,1203,1273,1350,1418", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,969,1037,1115,1198,1268,1345,1413,1533"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1251,1342,4995,5094,5265,5425,5505,5600,5689,5771,5839,5907,5985,6149,6506,6583,6651", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "1337,1418,5089,5188,5345,5500,5595,5684,5766,5834,5902,5980,6063,6214,6578,6646,6766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "534,629,736,833,933,1036,1140,6300", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "624,731,828,928,1031,1135,1246,6396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,376,484,560,655,768,905,1028,1159,1245,1342,1435,1531,1642,1764,1866,1987,2107,2245,2412,2531,2643,2759,2878,2972,3066,3171,3289,3393,3497,3596,3721,3852,3956,4056,4128,4203,4284,4365,4470,4546,4633,4730,4827,4918,5022,5106,5207,5304,5405,5521,5597,5695", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "160,268,371,479,555,650,763,900,1023,1154,1240,1337,1430,1526,1637,1759,1861,1982,2102,2240,2407,2526,2638,2754,2873,2967,3061,3166,3284,3388,3492,3591,3716,3847,3951,4051,4123,4198,4279,4360,4465,4541,4628,4725,4822,4913,5017,5101,5202,5299,5400,5516,5592,5690,5782"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,323,426,1423,1499,1594,1707,1844,1967,2098,2184,2281,2374,2470,2581,2703,2805,2926,3046,3184,3351,3470,3582,3698,3817,3911,4005,4110,4228,4332,4436,4535,4660,4791,4895,5193,5350,6068,6219,6401,6771,6847,6934,7031,7128,7219,7323,7407,7508,7605,7706,7822,7898,7996", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "210,318,421,529,1494,1589,1702,1839,1962,2093,2179,2276,2369,2465,2576,2698,2800,2921,3041,3179,3346,3465,3577,3693,3812,3906,4000,4105,4223,4327,4431,4530,4655,4786,4890,4990,5260,5420,6144,6295,6501,6842,6929,7026,7123,7214,7318,7402,7503,7600,7701,7817,7893,7991,8083"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1284,1381,5094,5191,5365,5531,5608,5699,5791,5876,5947,6017,6097,6267,6628,6707,6777", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "1376,1463,5186,5287,5446,5603,5694,5786,5871,5942,6012,6092,6177,6335,6702,6772,6890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "558,656,758,855,959,1063,1168,6424", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "651,753,850,954,1058,1163,1279,6520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,275,398,508,588,678,786,918,1033,1173,1254,1350,1441,1535,1647,1768,1869,2006,2142,2271,2447,2568,2684,2806,2925,3017,3111,3224,3350,3446,3544,3649,3786,3931,4036,4134,4207,4287,4372,4456,4559,4635,4714,4807,4906,4995,5089,5172,5276,5369,5466,5595,5671,5772", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "160,270,393,503,583,673,781,913,1028,1168,1249,1345,1436,1530,1642,1763,1864,2001,2137,2266,2442,2563,2679,2801,2920,3012,3106,3219,3345,3441,3539,3644,3781,3926,4031,4129,4202,4282,4367,4451,4554,4630,4709,4802,4901,4990,5084,5167,5271,5364,5461,5590,5666,5767,5860"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,325,448,1468,1548,1638,1746,1878,1993,2133,2214,2310,2401,2495,2607,2728,2829,2966,3102,3231,3407,3528,3644,3766,3885,3977,4071,4184,4310,4406,4504,4609,4746,4891,4996,5292,5451,6182,6340,6525,6895,6971,7050,7143,7242,7331,7425,7508,7612,7705,7802,7931,8007,8108", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "210,320,443,553,1543,1633,1741,1873,1988,2128,2209,2305,2396,2490,2602,2723,2824,2961,3097,3226,3402,3523,3639,3761,3880,3972,4066,4179,4305,4401,4499,4604,4741,4886,4991,5089,5360,5526,6262,6419,6623,6966,7045,7138,7237,7326,7420,7503,7607,7700,7797,7926,8002,8103,8196"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,978,1045,1130,1216,1288,1364,1430", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,973,1040,1125,1211,1283,1359,1425,1545"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1304,1394,5152,5248,5422,5587,5670,5765,5852,5937,6003,6070,6155,6324,6680,6756,6822", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "1389,1473,5243,5345,5502,5665,5760,5847,5932,5998,6065,6150,6236,6391,6751,6817,6937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,656,758,855,965,1071,1189,6481", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "651,753,850,960,1066,1184,1299,6577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,396,511,591,692,811,957,1081,1225,1307,1403,1491,1585,1697,1819,1920,2054,2185,2313,2488,2609,2730,2855,2981,3070,3167,3284,3408,3505,3608,3710,3846,3988,4091,4185,4257,4337,4420,4505,4603,4679,4758,4853,4948,5041,5140,5223,5322,5417,5519,5637,5714,5816", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "166,281,391,506,586,687,806,952,1076,1220,1302,1398,1486,1580,1692,1814,1915,2049,2180,2308,2483,2604,2725,2850,2976,3065,3162,3279,3403,3500,3603,3705,3841,3983,4086,4180,4252,4332,4415,4500,4598,4674,4753,4848,4943,5036,5135,5218,5317,5412,5514,5632,5709,5811,5903"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,336,446,1478,1558,1659,1778,1924,2048,2192,2274,2370,2458,2552,2664,2786,2887,3021,3152,3280,3455,3576,3697,3822,3948,4037,4134,4251,4375,4472,4575,4677,4813,4955,5058,5350,5507,6241,6396,6582,6942,7018,7097,7192,7287,7380,7479,7562,7661,7756,7858,7976,8053,8155", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "216,331,441,556,1553,1654,1773,1919,2043,2187,2269,2365,2453,2547,2659,2781,2882,3016,3147,3275,3450,3571,3692,3817,3943,4032,4129,4246,4370,4467,4570,4672,4808,4950,5053,5147,5417,5582,6319,6476,6675,7013,7092,7187,7282,7375,7474,7557,7656,7751,7853,7971,8048,8150,8242"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,403,515,597,689,799,933,1049,1199,1280,1379,1466,1559,1680,1796,1900,2041,2183,2309,2476,2599,2709,2824,2947,3035,3126,3250,3372,3467,3565,3673,3814,3962,4072,4167,4239,4320,4402,4488,4589,4665,4745,4841,4937,5028,5126,5208,5306,5400,5499,5610,5686,5782", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "164,278,398,510,592,684,794,928,1044,1194,1275,1374,1461,1554,1675,1791,1895,2036,2178,2304,2471,2594,2704,2819,2942,3030,3121,3245,3367,3462,3560,3668,3809,3957,4067,4162,4234,4315,4397,4483,4584,4660,4740,4836,4932,5023,5121,5203,5301,5395,5494,5605,5681,5777,5867"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,333,453,1496,1578,1670,1780,1914,2030,2180,2261,2360,2447,2540,2661,2777,2881,3022,3164,3290,3457,3580,3690,3805,3928,4016,4107,4231,4353,4448,4546,4654,4795,4943,5053,5339,5500,6233,6388,6575,6943,7019,7099,7195,7291,7382,7480,7562,7660,7754,7853,7964,8040,8136", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "214,328,448,560,1573,1665,1775,1909,2025,2175,2256,2355,2442,2535,2656,2772,2876,3017,3159,3285,3452,3575,3685,3800,3923,4011,4102,4226,4348,4443,4541,4649,4790,4938,5048,5143,5406,5576,6310,6469,6671,7014,7094,7190,7286,7377,7475,7557,7655,7749,7848,7959,8035,8131,8221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1318,1413,5148,5241,5411,5581,5659,5756,5845,5930,5998,6067,6148,6315,6676,6757,6823", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "1408,1491,5236,5334,5495,5654,5751,5840,5925,5993,6062,6143,6228,6383,6752,6818,6938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "565,663,766,871,972,1085,1191,6474", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "658,761,866,967,1080,1186,1313,6570"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "568,664,766,865,962,1068,1173,6544", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "659,761,860,957,1063,1168,1294,6640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1000,1069,1155,1246,1322,1404,1475", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,995,1064,1150,1241,1317,1399,1470,1590"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1299,1396,5189,5293,5468,5640,5718,5809,5900,5986,6058,6127,6213,6386,6752,6834,6905", "endColumns": "96,83,103,102,88,77,90,90,85,71,68,85,90,75,81,70,119", "endOffsets": "1391,1475,5288,5391,5552,5713,5804,5895,5981,6053,6122,6208,6299,6457,6829,6900,7020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,402,518,601,695,807,945,1056,1211,1291,1385,1479,1576,1689,1814,1913,2050,2183,2330,2492,2621,2734,2852,2978,3073,3166,3283,3424,3526,3635,3745,3879,4020,4125,4227,4299,4382,4464,4546,4653,4729,4809,4906,5010,5105,5205,5288,5397,5493,5595,5710,5786,5899", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "167,282,397,513,596,690,802,940,1051,1206,1286,1380,1474,1571,1684,1809,1908,2045,2178,2325,2487,2616,2729,2847,2973,3068,3161,3278,3419,3521,3630,3740,3874,4015,4120,4222,4294,4377,4459,4541,4648,4724,4804,4901,5005,5100,5200,5283,5392,5488,5590,5705,5781,5894,5997"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,337,452,1480,1563,1657,1769,1907,2018,2173,2253,2347,2441,2538,2651,2776,2875,3012,3145,3292,3454,3583,3696,3814,3940,4035,4128,4245,4386,4488,4597,4707,4841,4982,5087,5396,5557,6304,6462,6645,7025,7101,7181,7278,7382,7477,7577,7660,7769,7865,7967,8082,8158,8271", "endColumns": "116,114,114,115,82,93,111,137,110,154,79,93,93,96,112,124,98,136,132,146,161,128,112,117,125,94,92,116,140,101,108,109,133,140,104,101,71,82,81,81,106,75,79,96,103,94,99,82,108,95,101,114,75,112,102", "endOffsets": "217,332,447,563,1558,1652,1764,1902,2013,2168,2248,2342,2436,2533,2646,2771,2870,3007,3140,3287,3449,3578,3691,3809,3935,4030,4123,4240,4381,4483,4592,4702,4836,4977,5082,5184,5463,5635,6381,6539,6747,7096,7176,7273,7377,7472,7572,7655,7764,7860,7962,8077,8153,8266,8369"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,284,388,500,580,673,784,920,1039,1182,1263,1359,1453,1551,1669,1802,1903,2039,2173,2295,2487,2606,2724,2843,2974,3069,3160,3275,3399,3497,3602,3708,3848,3991,4094,4206,4286,4362,4446,4529,4626,4703,4782,4877,4979,5070,5162,5244,5349,5443,5537,5673,5750,5858", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "167,279,383,495,575,668,779,915,1034,1177,1258,1354,1448,1546,1664,1797,1898,2034,2168,2290,2482,2601,2719,2838,2969,3064,3155,3270,3394,3492,3597,3703,3843,3986,4089,4201,4281,4357,4441,4524,4621,4698,4777,4872,4974,5065,5157,5239,5344,5438,5532,5668,5745,5853,5947"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,438,1455,1535,1628,1739,1875,1994,2137,2218,2314,2408,2506,2624,2757,2858,2994,3128,3250,3442,3561,3679,3798,3929,4024,4115,4230,4354,4452,4557,4663,4803,4946,5049,5357,5524,6253,6411,6595,6958,7035,7114,7209,7311,7402,7494,7576,7681,7775,7869,8005,8082,8190", "endColumns": "116,111,103,111,79,92,110,135,118,142,80,95,93,97,117,132,100,135,133,121,191,118,117,118,130,94,90,114,123,97,104,105,139,142,102,111,79,75,83,82,96,76,78,94,101,90,91,81,104,93,93,135,76,107,93", "endOffsets": "217,329,433,545,1530,1623,1734,1870,1989,2132,2213,2309,2403,2501,2619,2752,2853,2989,3123,3245,3437,3556,3674,3793,3924,4019,4110,4225,4349,4447,4552,4658,4798,4941,5044,5156,5432,5595,6332,6489,6687,7030,7109,7204,7306,7397,7489,7571,7676,7770,7864,8000,8077,8185,8279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,647,749,851,952,1055,1162,6494", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "642,744,846,947,1050,1157,1267,6590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1272,1367,5161,5258,5437,5600,5682,5778,5867,5954,6018,6082,6165,6337,6692,6771,6837", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "1362,1450,5253,5352,5519,5677,5773,5862,5949,6013,6077,6160,6248,6406,6766,6832,6953"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1288,1381,5145,5239,5423,5587,5667,5756,5844,5926,5997,6067,6150,6319,6671,6756,6826", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "1376,1460,5234,5337,5504,5662,5751,5839,5921,5992,6062,6145,6232,6386,6751,6821,6944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "557,655,757,857,958,1064,1167,6474", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "650,752,852,953,1059,1162,1283,6570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,394,507,584,677,788,907,1018,1159,1239,1346,1435,1528,1638,1757,1863,2006,2148,2282,2455,2583,2704,2830,2949,3039,3133,3255,3384,3479,3589,3694,3839,3988,4092,4187,4268,4346,4428,4511,4607,4690,4773,4869,4971,5063,5157,5242,5346,5438,5533,5675,5761,5876", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "163,274,389,502,579,672,783,902,1013,1154,1234,1341,1430,1523,1633,1752,1858,2001,2143,2277,2450,2578,2699,2825,2944,3034,3128,3250,3379,3474,3584,3689,3834,3983,4087,4182,4263,4341,4423,4506,4602,4685,4768,4864,4966,5058,5152,5237,5341,5433,5528,5670,5756,5871,5963"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,329,444,1465,1542,1635,1746,1865,1976,2117,2197,2304,2393,2486,2596,2715,2821,2964,3106,3240,3413,3541,3662,3788,3907,3997,4091,4213,4342,4437,4547,4652,4797,4946,5050,5342,5509,6237,6391,6575,6949,7032,7115,7211,7313,7405,7499,7584,7688,7780,7875,8017,8103,8218", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "213,324,439,552,1537,1630,1741,1860,1971,2112,2192,2299,2388,2481,2591,2710,2816,2959,3101,3235,3408,3536,3657,3783,3902,3992,4086,4208,4337,4432,4542,4647,4792,4941,5045,5140,5418,5582,6314,6469,6666,7027,7110,7206,7308,7400,7494,7579,7683,7775,7870,8012,8098,8213,8305"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,880,945,1018,1093,1161,1234,1300", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,875,940,1013,1088,1156,1229,1295,1411"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1166,1243,4406,4491,4651,4799,4873,4950,5028,5103,5168,5233,5306,5460,5795,5868,5934", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "1238,1314,4486,4577,4723,4868,4945,5023,5098,5163,5228,5301,5376,5523,5863,5929,6045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,594,695,789,883,976,1070,5606", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "589,690,784,878,971,1065,1161,5702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,607,707,811,912,1023,1101,1193,1273,1358,1460,1570,1667,1771,1873,1977,2089,2192,2289,2390,2493,2574,2665,2766,2871,2957,3051,3145,3248,3357,3453,3539,3608,3679,3758,3836,3924,4000,4077,4171,4261,4350,4441,4520,4612,4704,4796,4900,4976,5064", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "150,249,349,447,519,602,702,806,907,1018,1096,1188,1268,1353,1455,1565,1662,1766,1868,1972,2084,2187,2284,2385,2488,2569,2660,2761,2866,2952,3046,3140,3243,3352,3448,3534,3603,3674,3753,3831,3919,3995,4072,4166,4256,4345,4436,4515,4607,4699,4791,4895,4971,5059,5145"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,304,404,1319,1391,1474,1574,1678,1779,1890,1968,2060,2140,2225,2327,2437,2534,2638,2740,2844,2956,3059,3156,3257,3360,3441,3532,3633,3738,3824,3918,4012,4115,4224,4320,4582,4728,5381,5528,5707,6050,6126,6203,6297,6387,6476,6567,6646,6738,6830,6922,7026,7102,7190", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "200,299,399,497,1386,1469,1569,1673,1774,1885,1963,2055,2135,2220,2322,2432,2529,2633,2735,2839,2951,3054,3151,3252,3355,3436,3527,3628,3733,3819,3913,4007,4110,4219,4315,4401,4646,4794,5455,5601,5790,6121,6198,6292,6382,6471,6562,6641,6733,6825,6917,7021,7097,7185,7271"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1263,1356,4984,5079,5260,5425,5508,5608,5696,5780,5848,5914,5994,6164,6530,6608,6676", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "1351,1435,5074,5174,5339,5503,5603,5691,5775,5843,5909,5989,6077,6230,6603,6671,6789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,275,389,500,580,672,782,911,1029,1167,1248,1342,1427,1520,1631,1748,1847,1980,2112,2230,2397,2511,2623,2738,2850,2936,3030,3150,3275,3372,3470,3572,3703,3839,3947,4044,4125,4206,4288,4369,4482,4558,4638,4734,4830,4922,5013,5097,5199,5295,5389,5506,5582,5685", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "161,270,384,495,575,667,777,906,1024,1162,1243,1337,1422,1515,1626,1743,1842,1975,2107,2225,2392,2506,2618,2733,2845,2931,3025,3145,3270,3367,3465,3567,3698,3834,3942,4039,4120,4201,4283,4364,4477,4553,4633,4729,4825,4917,5008,5092,5194,5290,5384,5501,5577,5680,5769"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,439,1440,1520,1612,1722,1851,1969,2107,2188,2282,2367,2460,2571,2688,2787,2920,3052,3170,3337,3451,3563,3678,3790,3876,3970,4090,4215,4312,4410,4512,4643,4779,4887,5179,5344,6082,6235,6417,6794,6870,6950,7046,7142,7234,7325,7409,7511,7607,7701,7818,7894,7997", "endColumns": "110,108,113,110,79,91,109,128,117,137,80,93,84,92,110,116,98,132,131,117,166,113,111,114,111,85,93,119,124,96,97,101,130,135,107,96,80,80,81,80,112,75,79,95,95,91,90,83,101,95,93,116,75,102,88", "endOffsets": "211,320,434,545,1515,1607,1717,1846,1964,2102,2183,2277,2362,2455,2566,2683,2782,2915,3047,3165,3332,3446,3558,3673,3785,3871,3965,4085,4210,4307,4405,4507,4638,4774,4882,4979,5255,5420,6159,6311,6525,6865,6945,7041,7137,7229,7320,7404,7506,7602,7696,7813,7889,7992,8081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,647,749,847,944,1046,1152,6316", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "642,744,842,939,1041,1147,1258,6412"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1286,1379,5112,5214,5386,5548,5630,5719,5807,5889,5959,6030,6115,6285,6636,6716,6786", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "1374,1458,5209,5310,5465,5625,5714,5802,5884,5954,6025,6110,6198,6352,6711,6781,6904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,677,786,920,1032,1175,1255,1351,1440,1532,1644,1764,1865,2002,2135,2266,2451,2574,2694,2820,2938,3027,3124,3246,3372,3466,3567,3672,3812,3959,4063,4158,4229,4307,4389,4472,4567,4643,4725,4820,4920,5011,5107,5192,5295,5387,5485,5599,5675,5776", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "164,276,390,504,581,672,781,915,1027,1170,1250,1346,1435,1527,1639,1759,1860,1997,2130,2261,2446,2569,2689,2815,2933,3022,3119,3241,3367,3461,3562,3667,3807,3954,4058,4153,4224,4302,4384,4467,4562,4638,4720,4815,4915,5006,5102,5187,5290,5382,5480,5594,5670,5771,5873"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,445,1463,1540,1631,1740,1874,1986,2129,2209,2305,2394,2486,2598,2718,2819,2956,3089,3220,3405,3528,3648,3774,3892,3981,4078,4200,4326,4420,4521,4626,4766,4913,5017,5315,5470,6203,6357,6541,6909,6985,7067,7162,7262,7353,7449,7534,7637,7729,7827,7941,8017,8118", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "214,326,440,554,1535,1626,1735,1869,1981,2124,2204,2300,2389,2481,2593,2713,2814,2951,3084,3215,3400,3523,3643,3769,3887,3976,4073,4195,4321,4415,4516,4621,4761,4908,5012,5107,5381,5543,6280,6435,6631,6980,7062,7157,7257,7348,7444,7529,7632,7724,7822,7936,8012,8113,8215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "559,659,761,862,963,1068,1173,6440", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "654,756,857,958,1063,1168,1281,6536"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "549,646,748,847,947,1050,1163,6337", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "641,743,842,942,1045,1158,1274,6433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1279,1375,4975,5081,5256,5426,5511,5604,5698,5779,5849,5919,6009,6185,6541,6618,6684", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "1370,1456,5076,5176,5343,5506,5599,5693,5774,5844,5914,6004,6095,6252,6613,6679,6793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,386,499,578,669,778,911,1027,1164,1244,1343,1428,1520,1635,1754,1858,1981,2100,2223,2378,2501,2620,2737,2853,2939,3035,3149,3278,3369,3471,3574,3692,3818,3922,4013,4088,4166,4251,4331,4434,4510,4589,4684,4778,4869,4965,5047,5144,5238,5336,5448,5524,5633", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "165,278,381,494,573,664,773,906,1022,1159,1239,1338,1423,1515,1630,1749,1853,1976,2095,2218,2373,2496,2615,2732,2848,2934,3030,3144,3273,3364,3466,3569,3687,3813,3917,4008,4083,4161,4246,4326,4429,4505,4584,4679,4773,4864,4960,5042,5139,5233,5331,5443,5519,5628,5728"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,333,436,1461,1540,1631,1740,1873,1989,2126,2206,2305,2390,2482,2597,2716,2820,2943,3062,3185,3340,3463,3582,3699,3815,3901,3997,4111,4240,4331,4433,4536,4654,4780,4884,5181,5348,6100,6257,6438,6798,6874,6953,7048,7142,7233,7329,7411,7508,7602,7700,7812,7888,7997", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "215,328,431,544,1535,1626,1735,1868,1984,2121,2201,2300,2385,2477,2592,2711,2815,2938,3057,3180,3335,3458,3577,3694,3810,3896,3992,4106,4235,4326,4428,4531,4649,4775,4879,4970,5251,5421,6180,6332,6536,6869,6948,7043,7137,7228,7324,7406,7503,7597,7695,7807,7883,7992,8092"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-v23_values-v23.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eed21004deab6640172f1382b4314917\\transformed\\work-runtime-2.9.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "564,659,761,859,962,1068,1173,6372", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "654,756,854,957,1063,1168,1288,6468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1293,1386,5067,5163,5331,5491,5569,5660,5751,5835,5903,5969,6051,6219,6575,6652,6723", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "1381,1461,5158,5253,5408,5564,5655,5746,5830,5898,5964,6046,6131,6286,6647,6718,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,289,399,514,592,689,804,933,1049,1193,1276,1373,1463,1558,1670,1793,1893,2024,2153,2280,2451,2573,2688,2806,2925,3016,3109,3225,3355,3456,3555,3656,3782,3913,4017,4115,4188,4266,4349,4430,4532,4608,4690,4787,4887,4977,5077,5162,5267,5364,5466,5579,5655,5755", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "166,284,394,509,587,684,799,928,1044,1188,1271,1368,1458,1553,1665,1788,1888,2019,2148,2275,2446,2568,2683,2801,2920,3011,3104,3220,3350,3451,3550,3651,3777,3908,4012,4110,4183,4261,4344,4425,4527,4603,4685,4782,4882,4972,5072,5157,5262,5359,5461,5574,5650,5750,5845"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,339,449,1466,1544,1641,1756,1885,2001,2145,2228,2325,2415,2510,2622,2745,2845,2976,3105,3232,3403,3525,3640,3758,3877,3968,4061,4177,4307,4408,4507,4608,4734,4865,4969,5258,5413,6136,6291,6473,6845,6921,7003,7100,7200,7290,7390,7475,7580,7677,7779,7892,7968,8068", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "216,334,444,559,1539,1636,1751,1880,1996,2140,2223,2320,2410,2505,2617,2740,2840,2971,3100,3227,3398,3520,3635,3753,3872,3963,4056,4172,4302,4403,4502,4603,4729,4860,4964,5062,5326,5486,6214,6367,6570,6916,6998,7095,7195,7285,7385,7470,7575,7672,7774,7887,7963,8063,8158"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,644,746,845,944,1048,1151,6243", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "639,741,840,939,1043,1146,1262,6339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4072,4148,4231,4314,4412,4488,4568,4665,4762,4858,4953,5037,5139,5236,5335,5451,5527,5623", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4067,4143,4226,4309,4407,4483,4563,4660,4757,4853,4948,5032,5134,5231,5330,5446,5522,5618,5709"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,438,1441,1519,1610,1719,1851,1963,2095,2175,2270,2357,2450,2565,2686,2786,2909,3028,3152,3310,3427,3539,3659,3781,3869,3963,4076,4196,4289,4387,4485,4610,4745,4847,5134,5295,6004,6160,6344,6706,6782,6862,6959,7056,7152,7247,7331,7433,7530,7629,7745,7821,7917", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "214,326,433,543,1514,1605,1714,1846,1958,2090,2170,2265,2352,2445,2560,2681,2781,2904,3023,3147,3305,3422,3534,3654,3776,3864,3958,4071,4191,4284,4382,4480,4605,4740,4842,4936,5203,5366,6082,6238,6437,6777,6857,6954,7051,7147,7242,7326,7428,7525,7624,7740,7816,7912,8003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1267,1359,4941,5035,5208,5371,5453,5542,5631,5715,5780,5844,5922,6087,6442,6519,6585", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "1354,1436,5030,5129,5290,5448,5537,5626,5710,5775,5839,5917,5999,6155,6514,6580,6701"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "539,635,737,834,932,1039,1148,6281", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "630,732,829,927,1034,1143,1261,6377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1266,1358,4991,5086,5261,5418,5495,5584,5673,5755,5820,5885,5966,6130,6479,6557,6624", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "1353,1433,5081,5180,5338,5490,5579,5668,5750,5815,5880,5961,6045,6195,6552,6619,6739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,379,489,564,655,764,904,1022,1167,1247,1343,1428,1518,1628,1747,1848,1971,2089,2221,2390,2516,2629,2748,2864,2950,3044,3159,3288,3382,3496,3594,3717,3848,3949,4042,4118,4193,4273,4354,4451,4527,4607,4704,4799,4890,4985,5068,5169,5267,5367,5480,5556,5654", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "162,271,374,484,559,650,759,899,1017,1162,1242,1338,1423,1513,1623,1742,1843,1966,2084,2216,2385,2511,2624,2743,2859,2945,3039,3154,3283,3377,3491,3589,3712,3843,3944,4037,4113,4188,4268,4349,4446,4522,4602,4699,4794,4885,4980,5063,5164,5262,5362,5475,5551,5649,5744"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,326,429,1438,1513,1604,1713,1853,1971,2116,2196,2292,2377,2467,2577,2696,2797,2920,3038,3170,3339,3465,3578,3697,3813,3899,3993,4108,4237,4331,4445,4543,4666,4797,4898,5185,5343,6050,6200,6382,6744,6820,6900,6997,7092,7183,7278,7361,7462,7560,7660,7773,7849,7947", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "212,321,424,534,1508,1599,1708,1848,1966,2111,2191,2287,2372,2462,2572,2691,2792,2915,3033,3165,3334,3460,3573,3692,3808,3894,3988,4103,4232,4326,4440,4538,4661,4792,4893,4986,5256,5413,6125,6276,6474,6815,6895,6992,7087,7178,7273,7356,7457,7555,7655,7768,7844,7942,8037"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "570,668,770,869,971,1075,1179,6544", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "663,765,864,966,1070,1174,1292,6640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,285,403,520,595,685,794,934,1049,1192,1272,1369,1466,1563,1680,1803,1905,2051,2193,2319,2507,2629,2743,2863,2993,3091,3192,3312,3433,3531,3634,3735,3875,4023,4128,4232,4315,4392,4479,4562,4665,4741,4822,4920,5028,5122,5218,5302,5414,5511,5609,5737,5813,5919", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "166,280,398,515,590,680,789,929,1044,1187,1267,1364,1461,1558,1675,1798,1900,2046,2188,2314,2502,2624,2738,2858,2988,3086,3187,3307,3428,3526,3629,3730,3870,4018,4123,4227,4310,4387,4474,4557,4660,4736,4817,4915,5023,5117,5213,5297,5409,5506,5604,5732,5808,5914,6008"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,335,453,1484,1559,1649,1758,1898,2013,2156,2236,2333,2430,2527,2644,2767,2869,3015,3157,3283,3471,3593,3707,3827,3957,4055,4156,4276,4397,4495,4598,4699,4839,4987,5092,5396,5566,6298,6461,6645,7017,7093,7174,7272,7380,7474,7570,7654,7766,7863,7961,8089,8165,8271", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "216,330,448,565,1554,1644,1753,1893,2008,2151,2231,2328,2425,2522,2639,2762,2864,3010,3152,3278,3466,3588,3702,3822,3952,4050,4151,4271,4392,4490,4593,4694,4834,4982,5087,5191,5474,5638,6380,6539,6743,7088,7169,7267,7375,7469,7565,7649,7761,7858,7956,8084,8160,8266,8360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1297,1396,5196,5296,5479,5643,5722,5814,5906,5993,6064,6132,6213,6385,6748,6826,6895", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "1391,1479,5291,5391,5561,5717,5809,5901,5988,6059,6127,6208,6293,6456,6821,6890,7012"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,963,1027,1105,1187,1260,1337,1403,1523"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1266,1358,4940,5034,5205,5367,5449,5539,5628,5712,5777,5841,5919,6084,6439,6516,6582", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "1353,1435,5029,5128,5286,5444,5534,5623,5707,5772,5836,5914,5996,6152,6511,6577,6697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4070,4146,4229,4312,4410,4486,4566,4663,4760,4856,4951,5035,5137,5234,5333,5445,5521,5617", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,71,75,82,82,97,75,79,96,96,95,94,83,101,96,98,111,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4065,4141,4224,4307,4405,4481,4561,4658,4755,4851,4946,5030,5132,5229,5328,5440,5516,5612,5703"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,438,1440,1518,1609,1718,1850,1962,2094,2174,2269,2356,2449,2564,2685,2785,2908,3027,3151,3309,3426,3538,3658,3780,3868,3962,4075,4195,4288,4386,4484,4609,4744,4846,5133,5291,6001,6157,6341,6702,6778,6858,6955,7052,7148,7243,7327,7429,7526,7625,7737,7813,7909", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,71,75,82,82,97,75,79,96,96,95,94,83,101,96,98,111,75,95,90", "endOffsets": "214,326,433,543,1513,1604,1713,1845,1957,2089,2169,2264,2351,2444,2559,2680,2780,2903,3022,3146,3304,3421,3533,3653,3775,3863,3957,4070,4190,4283,4381,4479,4604,4739,4841,4935,5200,5362,6079,6235,6434,6773,6853,6950,7047,7143,7238,7322,7424,7521,7620,7732,7808,7904,7995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,644,746,845,944,1048,1150,6240", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "639,741,840,939,1043,1145,1261,6336"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "584,686,789,891,995,1098,1199,6628", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "681,784,886,990,1093,1194,1316,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,293,415,534,614,704,812,960,1074,1227,1308,1409,1505,1603,1722,1851,1957,2089,2227,2354,2554,2684,2804,2931,3062,3159,3253,3367,3494,3589,3687,3796,3933,4075,4185,4289,4361,4443,4526,4619,4723,4799,4883,4977,5086,5177,5284,5370,5481,5579,5687,5815,5891,5994", "endColumns": "115,121,121,118,79,89,107,147,113,152,80,100,95,97,118,128,105,131,137,126,199,129,119,126,130,96,93,113,126,94,97,108,136,141,109,103,71,81,82,92,103,75,83,93,108,90,106,85,110,97,107,127,75,102,92", "endOffsets": "166,288,410,529,609,699,807,955,1069,1222,1303,1404,1500,1598,1717,1846,1952,2084,2222,2349,2549,2679,2799,2926,3057,3154,3248,3362,3489,3584,3682,3791,3928,4070,4180,4284,4356,4438,4521,4614,4718,4794,4878,4972,5081,5172,5279,5365,5476,5574,5682,5810,5886,5989,6082"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,343,465,1503,1583,1673,1781,1929,2043,2196,2277,2378,2474,2572,2691,2820,2926,3058,3196,3323,3523,3653,3773,3900,4031,4128,4222,4336,4463,4558,4656,4765,4902,5044,5154,5461,5623,6374,6535,6729,7095,7171,7255,7349,7458,7549,7656,7742,7853,7951,8059,8187,8263,8366", "endColumns": "115,121,121,118,79,89,107,147,113,152,80,100,95,97,118,128,105,131,137,126,199,129,119,126,130,96,93,113,126,94,97,108,136,141,109,103,71,81,82,92,103,75,83,93,108,90,106,85,110,97,107,127,75,102,92", "endOffsets": "216,338,460,579,1578,1668,1776,1924,2038,2191,2272,2373,2469,2567,2686,2815,2921,3053,3191,3318,3518,3648,3768,3895,4026,4123,4217,4331,4458,4553,4651,4760,4897,5039,5149,5253,5528,5700,6452,6623,6828,7166,7250,7344,7453,7544,7651,7737,7848,7946,8054,8182,8258,8361,8454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1009,1076,1162,1249,1327,1403,1470", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1004,1071,1157,1244,1322,1398,1465,1584"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1321,1416,5258,5357,5533,5705,5791,5892,5979,6067,6134,6201,6287,6457,6833,6909,6976", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "1411,1498,5352,5456,5618,5786,5887,5974,6062,6129,6196,6282,6369,6530,6904,6971,7090"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "581,684,787,889,995,1093,1193,6597", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "679,782,884,990,1088,1188,1296,6693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,295,407,531,615,706,815,955,1072,1221,1301,1405,1499,1594,1702,1822,1931,2073,2212,2342,2503,2629,2777,2931,3057,3152,3243,3355,3475,3575,3691,3796,3937,4081,4187,4290,4361,4445,4532,4618,4721,4797,4878,4975,5080,5171,5270,5353,5460,5555,5655,5786,5862,5962", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "175,290,402,526,610,701,810,950,1067,1216,1296,1400,1494,1589,1697,1817,1926,2068,2207,2337,2498,2624,2772,2926,3052,3147,3238,3350,3470,3570,3686,3791,3932,4076,4182,4285,4356,4440,4527,4613,4716,4792,4873,4970,5075,5166,5265,5348,5455,5550,5650,5781,5857,5957,6047"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,230,345,457,1490,1574,1665,1774,1914,2031,2180,2260,2364,2458,2553,2661,2781,2890,3032,3171,3301,3462,3588,3736,3890,4016,4111,4202,4314,4434,4534,4650,4755,4896,5040,5146,5440,5597,6347,6511,6698,7070,7146,7227,7324,7429,7520,7619,7702,7809,7904,8004,8135,8211,8311", "endColumns": "124,114,111,123,83,90,108,139,116,148,79,103,93,94,107,119,108,141,138,129,160,125,147,153,125,94,90,111,119,99,115,104,140,143,105,102,70,83,86,85,102,75,80,96,104,90,98,82,106,94,99,130,75,99,89", "endOffsets": "225,340,452,576,1569,1660,1769,1909,2026,2175,2255,2359,2453,2548,2656,2776,2885,3027,3166,3296,3457,3583,3731,3885,4011,4106,4197,4309,4429,4529,4645,4750,4891,5035,5141,5244,5506,5676,6429,6592,6796,7141,7222,7319,7424,7515,7614,7697,7804,7899,7999,8130,8206,8306,8396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,988,1054,1144,1237,1314,1395,1463", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,983,1049,1139,1232,1309,1390,1458,1578"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1301,1400,5249,5343,5511,5681,5763,5859,5946,6032,6098,6164,6254,6434,6801,6882,6950", "endColumns": "98,89,93,96,85,81,95,86,85,65,65,89,92,76,80,67,119", "endOffsets": "1395,1485,5338,5435,5592,5758,5854,5941,6027,6093,6159,6249,6342,6506,6877,6945,7065"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "605,708,812,915,1017,1122,1228,6512", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "703,807,910,1012,1117,1223,1342,6608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,318,425,555,635,731,845,990,1110,1269,1351,1446,1535,1633,1749,1872,1972,2094,2221,2361,2525,2643,2756,2872,2996,3086,3179,3306,3439,3537,3645,3746,3867,3992,4091,4189,4266,4344,4430,4512,4624,4700,4780,4876,4974,5066,5160,5243,5344,5439,5535,5652,5728,5847", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "183,313,420,550,630,726,840,985,1105,1264,1346,1441,1530,1628,1744,1867,1967,2089,2216,2356,2520,2638,2751,2867,2991,3081,3174,3301,3434,3532,3640,3741,3862,3987,4086,4184,4261,4339,4425,4507,4619,4695,4775,4871,4969,5061,5155,5238,5339,5434,5530,5647,5723,5842,5956"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,238,368,475,1529,1609,1705,1819,1964,2084,2243,2325,2420,2509,2607,2723,2846,2946,3068,3195,3335,3499,3617,3730,3846,3970,4060,4153,4280,4413,4511,4619,4720,4841,4966,5065,5371,5531,6269,6430,6613,6998,7074,7154,7250,7348,7440,7534,7617,7718,7813,7909,8026,8102,8221", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "233,363,470,600,1604,1700,1814,1959,2079,2238,2320,2415,2504,2602,2718,2841,2941,3063,3190,3330,3494,3612,3725,3841,3965,4055,4148,4275,4408,4506,4614,4715,4836,4961,5060,5158,5443,5604,6350,6507,6720,7069,7149,7245,7343,7435,7529,7612,7713,7808,7904,8021,8097,8216,8330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,998,1065,1152,1238,1313,1394,1460", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,993,1060,1147,1233,1308,1389,1455,1581"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1347,1441,5163,5267,5448,5609,5693,5792,5881,5963,6029,6096,6183,6355,6725,6806,6872", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "1436,1524,5262,5366,5526,5688,5787,5876,5958,6024,6091,6178,6264,6425,6801,6867,6993"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,405,523,603,702,819,966,1090,1244,1330,1426,1521,1622,1736,1858,1959,2097,2229,2369,2545,2679,2795,2919,3040,3136,3231,3363,3496,3598,3700,3806,3945,4094,4204,4305,4388,4467,4553,4638,4737,4813,4892,4987,5085,5178,5272,5355,5457,5552,5649,5766,5842,5944", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "170,290,400,518,598,697,814,961,1085,1239,1325,1421,1516,1617,1731,1853,1954,2092,2224,2364,2540,2674,2790,2914,3035,3131,3226,3358,3491,3593,3695,3801,3940,4089,4199,4300,4383,4462,4548,4633,4732,4808,4887,4982,5080,5173,5267,5350,5452,5547,5644,5761,5837,5939,6042"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,345,455,1493,1573,1672,1789,1936,2060,2214,2300,2396,2491,2592,2706,2828,2929,3067,3199,3339,3515,3649,3765,3889,4010,4106,4201,4333,4466,4568,4670,4776,4915,5064,5174,5481,5656,6390,6551,6737,7108,7184,7263,7358,7456,7549,7643,7726,7828,7923,8020,8137,8213,8315", "endColumns": "119,119,109,117,79,98,116,146,123,153,85,95,94,100,113,121,100,137,131,139,175,133,115,123,120,95,94,131,132,101,101,105,138,148,109,100,82,78,85,84,98,75,78,94,97,92,93,82,101,94,96,116,75,101,102", "endOffsets": "220,340,450,568,1568,1667,1784,1931,2055,2209,2295,2391,2486,2587,2701,2823,2924,3062,3194,3334,3510,3644,3760,3884,4005,4101,4196,4328,4461,4563,4665,4771,4910,5059,5169,5270,5559,5730,6471,6631,6831,7179,7258,7353,7451,7544,7638,7721,7823,7918,8015,8132,8208,8310,8413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,671,774,874,977,1085,1191,6636", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "666,769,869,972,1080,1186,1303,6732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1012,1080,1161,1243,1318,1397,1467", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1007,1075,1156,1238,1313,1392,1462,1585"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1308,1407,5275,5376,5564,5735,5816,5910,5999,6089,6159,6227,6308,6476,6836,6915,6985", "endColumns": "98,85,100,104,91,80,93,88,89,69,67,80,81,74,78,69,122", "endOffsets": "1402,1488,5371,5476,5651,5811,5905,5994,6084,6154,6222,6303,6385,6546,6910,6980,7103"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,972,1039,1122,1207,1282,1357,1423", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,967,1034,1117,1202,1277,1352,1418,1535"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1284,1378,5149,5239,5408,5575,5656,5749,5837,5923,5990,6057,6140,6311,6685,6760,6826", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "1373,1456,5234,5331,5491,5651,5744,5832,5918,5985,6052,6135,6220,6381,6755,6821,6938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,287,399,513,591,684,796,927,1046,1181,1262,1362,1454,1550,1663,1789,1894,2034,2173,2301,2495,2619,2734,2854,2989,3082,3173,3293,3413,3510,3611,3713,3853,4000,4102,4201,4273,4352,4438,4525,4636,4722,4803,4902,5004,5097,5196,5277,5380,5475,5573,5709,5795,5893", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "164,282,394,508,586,679,791,922,1041,1176,1257,1357,1449,1545,1658,1784,1889,2029,2168,2296,2490,2614,2729,2849,2984,3077,3168,3288,3408,3505,3606,3708,3848,3995,4097,4196,4268,4347,4433,4520,4631,4717,4798,4897,4999,5092,5191,5272,5375,5470,5568,5704,5790,5888,5978"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,337,449,1461,1539,1632,1744,1875,1994,2129,2210,2310,2402,2498,2611,2737,2842,2982,3121,3249,3443,3567,3682,3802,3937,4030,4121,4241,4361,4458,4559,4661,4801,4948,5050,5336,5496,6225,6386,6574,6943,7029,7110,7209,7311,7404,7503,7584,7687,7782,7880,8016,8102,8200", "endColumns": "113,117,111,113,77,92,111,130,118,134,80,99,91,95,112,125,104,139,138,127,193,123,114,119,134,92,90,119,119,96,100,101,139,146,101,98,71,78,85,86,110,85,80,98,101,92,98,80,102,94,97,135,85,97,89", "endOffsets": "214,332,444,558,1534,1627,1739,1870,1989,2124,2205,2305,2397,2493,2606,2732,2837,2977,3116,3244,3438,3562,3677,3797,3932,4025,4116,4236,4356,4453,4554,4656,4796,4943,5045,5144,5403,5570,6306,6468,6680,7024,7105,7204,7306,7399,7498,7579,7682,7777,7875,8011,8097,8195,8285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "563,661,763,865,969,1072,1170,6473", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "656,758,860,964,1067,1165,1279,6569"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,967,1031,1117,1207,1276,1354,1421", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,962,1026,1112,1202,1271,1349,1416,1536"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1328,1421,5099,5195,5370,5532,5608,5696,5785,5866,5930,5994,6080,6250,6598,6676,6743", "endColumns": "92,87,95,98,87,75,87,88,80,63,63,85,89,68,77,66,119", "endOffsets": "1416,1504,5190,5289,5453,5603,5691,5780,5861,5925,5989,6075,6165,6314,6671,6738,6858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,319,422,550,630,722,832,972,1092,1243,1324,1420,1506,1596,1706,1824,1925,2054,2177,2308,2476,2600,2714,2837,2954,3041,3135,3249,3384,3476,3580,3679,3807,3946,4048,4140,4216,4290,4370,4451,4548,4624,4705,4803,4899,4994,5091,5174,5274,5371,5470,5588,5664,5764", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "185,314,417,545,625,717,827,967,1087,1238,1319,1415,1501,1591,1701,1819,1920,2049,2172,2303,2471,2595,2709,2832,2949,3036,3130,3244,3379,3471,3575,3674,3802,3941,4043,4135,4211,4285,4365,4446,4543,4619,4700,4798,4894,4989,5086,5169,5269,5366,5465,5583,5659,5759,5854"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,240,369,472,1509,1589,1681,1791,1931,2051,2202,2283,2379,2465,2555,2665,2783,2884,3013,3136,3267,3435,3559,3673,3796,3913,4000,4094,4208,4343,4435,4539,4638,4766,4905,5007,5294,5458,6170,6319,6501,6863,6939,7020,7118,7214,7309,7406,7489,7589,7686,7785,7903,7979,8079", "endColumns": "134,128,102,127,79,91,109,139,119,150,80,95,85,89,109,117,100,128,122,130,167,123,113,122,116,86,93,113,134,91,103,98,127,138,101,91,75,73,79,80,96,75,80,97,95,94,96,82,99,96,98,117,75,99,94", "endOffsets": "235,364,467,595,1584,1676,1786,1926,2046,2197,2278,2374,2460,2550,2660,2778,2879,3008,3131,3262,3430,3554,3668,3791,3908,3995,4089,4203,4338,4430,4534,4633,4761,4900,5002,5094,5365,5527,6245,6395,6593,6934,7015,7113,7209,7304,7401,7484,7584,7681,7780,7898,7974,8074,8169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "600,695,797,895,994,1102,1207,6400", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "690,792,890,989,1097,1202,1323,6496"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,969,1035,1116,1198,1272,1347,1414", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,964,1030,1111,1193,1267,1342,1409,1526"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1276,1363,4978,5072,5246,5411,5493,5596,5681,5764,5831,5897,5978,6142,6492,6567,6634", "endColumns": "86,78,93,97,85,81,102,84,82,66,65,80,81,73,74,66,116", "endOffsets": "1358,1437,5067,5165,5327,5488,5591,5676,5759,5826,5892,5973,6055,6211,6562,6629,6746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,277,387,502,578,670,781,916,1028,1159,1240,1339,1427,1520,1630,1749,1853,1982,2109,2227,2388,2506,2615,2728,2842,2931,3025,3142,3270,3373,3472,3573,3699,3831,3933,4038,4114,4193,4275,4355,4450,4528,4608,4705,4802,4895,4991,5074,5174,5270,5368,5484,5562,5662", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "162,272,382,497,573,665,776,911,1023,1154,1235,1334,1422,1515,1625,1744,1848,1977,2104,2222,2383,2501,2610,2723,2837,2926,3020,3137,3265,3368,3467,3568,3694,3826,3928,4033,4109,4188,4270,4350,4445,4523,4603,4700,4797,4890,4986,5069,5169,5265,5363,5479,5557,5657,5751"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,327,437,1442,1518,1610,1721,1856,1968,2099,2180,2279,2367,2460,2570,2689,2793,2922,3049,3167,3328,3446,3555,3668,3782,3871,3965,4082,4210,4313,4412,4513,4639,4771,4873,5170,5332,6060,6216,6397,6751,6829,6909,7006,7103,7196,7292,7375,7475,7571,7669,7785,7863,7963", "endColumns": "111,109,109,114,75,91,110,134,111,130,80,98,87,92,109,118,103,128,126,117,160,117,108,112,113,88,93,116,127,102,98,100,125,131,101,104,75,78,81,79,94,77,79,96,96,92,95,82,99,95,97,115,77,99,93", "endOffsets": "212,322,432,547,1513,1605,1716,1851,1963,2094,2175,2274,2362,2455,2565,2684,2788,2917,3044,3162,3323,3441,3550,3663,3777,3866,3960,4077,4205,4308,4407,4508,4634,4766,4868,4973,5241,5406,6137,6291,6487,6824,6904,7001,7098,7191,7287,7370,7470,7566,7664,7780,7858,7958,8052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "552,651,753,852,952,1053,1159,6296", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "646,748,847,947,1048,1154,1271,6392"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1386,1477,5238,5339,5515,5684,5760,5847,5936,6020,6095,6167,6255,6430,6795,6872,6940", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "1472,1553,5334,5438,5602,5755,5842,5931,6015,6090,6162,6250,6340,6499,6867,6935,7055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "655,753,856,956,1059,1164,1267,6588", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "748,851,951,1054,1159,1262,1381,6684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,341,464,605,683,777,889,1025,1140,1281,1361,1462,1554,1650,1765,1881,1987,2126,2266,2397,2584,2705,2823,2944,3063,3156,3249,3373,3504,3598,3695,3797,3939,4086,4190,4285,4357,4434,4519,4603,4709,4785,4867,4958,5057,5144,5239,5325,5429,5525,5626,5740,5816,5916", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "193,336,459,600,678,772,884,1020,1135,1276,1356,1457,1549,1645,1760,1876,1982,2121,2261,2392,2579,2700,2818,2939,3058,3151,3244,3368,3499,3593,3690,3792,3934,4081,4185,4280,4352,4429,4514,4598,4704,4780,4862,4953,5052,5139,5234,5320,5424,5520,5621,5735,5811,5911,6002"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,248,391,514,1558,1636,1730,1842,1978,2093,2234,2314,2415,2507,2603,2718,2834,2940,3079,3219,3350,3537,3658,3776,3897,4016,4109,4202,4326,4457,4551,4648,4750,4892,5039,5143,5443,5607,6345,6504,6689,7060,7136,7218,7309,7408,7495,7590,7676,7780,7876,7977,8091,8167,8267", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "243,386,509,650,1631,1725,1837,1973,2088,2229,2309,2410,2502,2598,2713,2829,2935,3074,3214,3345,3532,3653,3771,3892,4011,4104,4197,4321,4452,4546,4643,4745,4887,5034,5138,5233,5510,5679,6425,6583,6790,7131,7213,7304,7403,7490,7585,7671,7775,7871,7972,8086,8162,8262,8353"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "553,651,753,854,952,1057,1169,6422", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "646,748,849,947,1052,1164,1283,6518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,975,1046,1129,1212,1285,1362,1428", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,970,1041,1124,1207,1280,1357,1423,1540"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1288,1380,5097,5189,5364,5523,5609,5703,5790,5871,5944,6015,6098,6262,6620,6697,6763", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "1375,1461,5184,5280,5442,5604,5698,5785,5866,5939,6010,6093,6176,6330,6692,6758,6875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,390,503,580,673,784,922,1036,1171,1252,1350,1438,1532,1646,1764,1867,2005,2145,2273,2445,2567,2684,2801,2918,3007,3103,3222,3356,3451,3555,3657,3796,3937,4040,4134,4213,4289,4370,4457,4554,4630,4709,4804,4900,4991,5089,5172,5276,5371,5471,5598,5674,5774", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "165,277,385,498,575,668,779,917,1031,1166,1247,1345,1433,1527,1641,1759,1862,2000,2140,2268,2440,2562,2679,2796,2913,3002,3098,3217,3351,3446,3550,3652,3791,3932,4035,4129,4208,4284,4365,4452,4549,4625,4704,4799,4895,4986,5084,5167,5271,5366,5466,5593,5669,5769,5860"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,332,440,1466,1543,1636,1747,1885,1999,2134,2215,2313,2401,2495,2609,2727,2830,2968,3108,3236,3408,3530,3647,3764,3881,3970,4066,4185,4319,4414,4518,4620,4759,4900,5003,5285,5447,6181,6335,6523,6880,6956,7035,7130,7226,7317,7415,7498,7602,7697,7797,7924,8000,8100", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "215,327,435,548,1538,1631,1742,1880,1994,2129,2210,2308,2396,2490,2604,2722,2825,2963,3103,3231,3403,3525,3642,3759,3876,3965,4061,4180,4314,4409,4513,4615,4754,4895,4998,5092,5359,5518,6257,6417,6615,6951,7030,7125,7221,7312,7410,7493,7597,7692,7792,7919,7995,8095,8186"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,403,517,599,693,805,953,1070,1208,1289,1384,1476,1575,1689,1806,1906,2033,2157,2289,2463,2590,2706,2824,2956,3049,3145,3261,3386,3482,3585,3684,3816,3952,4054,4153,4233,4312,4395,4478,4579,4655,4734,4829,4929,5020,5115,5199,5305,5402,5502,5618,5694,5791", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "168,281,398,512,594,688,800,948,1065,1203,1284,1379,1471,1570,1684,1801,1901,2028,2152,2284,2458,2585,2701,2819,2951,3044,3140,3256,3381,3477,3580,3679,3811,3947,4049,4148,4228,4307,4390,4473,4574,4650,4729,4824,4924,5015,5110,5194,5300,5397,5497,5613,5689,5786,5877"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,336,453,1476,1558,1652,1764,1912,2029,2167,2248,2343,2435,2534,2648,2765,2865,2992,3116,3248,3422,3549,3665,3783,3915,4008,4104,4220,4345,4441,4544,4643,4775,4911,5013,5308,5473,6194,6354,6538,6913,6989,7068,7163,7263,7354,7449,7533,7639,7736,7836,7952,8028,8125", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "218,331,448,562,1553,1647,1759,1907,2024,2162,2243,2338,2430,2529,2643,2760,2860,2987,3111,3243,3417,3544,3660,3778,3910,4003,4099,4215,4340,4436,4539,4638,4770,4906,5008,5107,5383,5547,6272,6432,6634,6984,7063,7158,7258,7349,7444,7528,7634,7731,7831,7947,8023,8120,8211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "567,669,771,871,971,1078,1182,6437", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "664,766,866,966,1073,1177,1296,6533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1301,1393,5112,5209,5388,5552,5628,5724,5811,5900,5965,6030,6111,6277,6639,6723,6793", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "1388,1471,5204,5303,5468,5623,5719,5806,5895,5960,6025,6106,6189,6349,6718,6788,6908"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1281,1376,5053,5148,5331,5500,5579,5673,5763,5844,5913,5982,6065,6241,6594,6672,6740", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "1371,1455,5143,5246,5418,5574,5668,5758,5839,5908,5977,6060,6147,6308,6667,6735,6849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "544,640,742,843,941,1051,1159,6396", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "635,737,838,936,1046,1154,1276,6492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,383,494,570,661,770,901,1013,1161,1242,1339,1427,1520,1632,1751,1853,1982,2111,2241,2401,2524,2644,2771,2888,2977,3070,3187,3305,3401,3500,3605,3741,3886,3991,4087,4167,4244,4333,4416,4513,4589,4671,4764,4863,4952,5045,5129,5230,5323,5417,5533,5609,5706", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "161,272,378,489,565,656,765,896,1008,1156,1237,1334,1422,1515,1627,1746,1848,1977,2106,2236,2396,2519,2639,2766,2883,2972,3065,3182,3300,3396,3495,3600,3736,3881,3986,4082,4162,4239,4328,4411,4508,4584,4666,4759,4858,4947,5040,5124,5225,5318,5412,5528,5604,5701,5790"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,433,1460,1536,1627,1736,1867,1979,2127,2208,2305,2393,2486,2598,2717,2819,2948,3077,3207,3367,3490,3610,3737,3854,3943,4036,4153,4271,4367,4466,4571,4707,4852,4957,5251,5423,6152,6313,6497,6854,6930,7012,7105,7204,7293,7386,7470,7571,7664,7758,7874,7950,8047", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "211,322,428,539,1531,1622,1731,1862,1974,2122,2203,2300,2388,2481,2593,2712,2814,2943,3072,3202,3362,3485,3605,3732,3849,3938,4031,4148,4266,4362,4461,4566,4702,4847,4952,5048,5326,5495,6236,6391,6589,6925,7007,7100,7199,7288,7381,7465,7566,7659,7753,7869,7945,8042,8131"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1288,1385,5101,5198,5372,5538,5615,5706,5798,5883,5955,6026,6106,6276,6637,6716,6786", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "1380,1467,5193,5294,5453,5610,5701,5793,5878,5950,6021,6101,6186,6344,6711,6781,6899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,401,512,593,683,791,923,1039,1179,1260,1356,1447,1541,1653,1774,1875,2012,2148,2277,2453,2574,2690,2812,2931,3023,3117,3231,3357,3453,3551,3656,3793,3938,4043,4141,4214,4294,4379,4463,4566,4642,4721,4814,4913,5002,5096,5179,5283,5376,5473,5602,5678,5781", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "161,272,396,507,588,678,786,918,1034,1174,1255,1351,1442,1536,1648,1769,1870,2007,2143,2272,2448,2569,2685,2807,2926,3018,3112,3226,3352,3448,3546,3651,3788,3933,4038,4136,4209,4289,4374,4458,4561,4637,4716,4809,4908,4997,5091,5174,5278,5371,5468,5597,5673,5776,5871"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,451,1472,1553,1643,1751,1883,1999,2139,2220,2316,2407,2501,2613,2734,2835,2972,3108,3237,3413,3534,3650,3772,3891,3983,4077,4191,4317,4413,4511,4616,4753,4898,5003,5299,5458,6191,6349,6534,6904,6980,7059,7152,7251,7340,7434,7517,7621,7714,7811,7940,8016,8119", "endColumns": "110,110,123,110,80,89,107,131,115,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,113,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,102,94", "endOffsets": "211,322,446,557,1548,1638,1746,1878,1994,2134,2215,2311,2402,2496,2608,2729,2830,2967,3103,3232,3408,3529,3645,3767,3886,3978,4072,4186,4312,4408,4506,4611,4748,4893,4998,5096,5367,5533,6271,6428,6632,6975,7054,7147,7246,7335,7429,7512,7616,7709,7806,7935,8011,8114,8209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "562,660,762,859,963,1067,1172,6433", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "655,757,854,958,1062,1167,1283,6529"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,388,500,575,676,797,931,1049,1188,1271,1372,1460,1556,1669,1791,1897,2036,2173,2306,2454,2576,2692,2812,2927,3016,3110,3229,3349,3445,3544,3648,3785,3928,4031,4128,4204,4278,4358,4439,4536,4611,4691,4788,4887,4982,5078,5161,5263,5359,5457,5591,5666,5763", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "164,277,383,495,570,671,792,926,1044,1183,1266,1367,1455,1551,1664,1786,1892,2031,2168,2301,2449,2571,2687,2807,2922,3011,3105,3224,3344,3440,3539,3643,3780,3923,4026,4123,4199,4273,4353,4434,4531,4606,4686,4783,4882,4977,5073,5156,5258,5354,5452,5586,5661,5758,5847"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,332,438,1434,1509,1610,1731,1865,1983,2122,2205,2306,2394,2490,2603,2725,2831,2970,3107,3240,3388,3510,3626,3746,3861,3950,4044,4163,4283,4379,4478,4582,4719,4862,4965,5255,5416,6144,6294,6476,6844,6919,6999,7096,7195,7290,7386,7469,7571,7667,7765,7899,7974,8071", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "214,327,433,545,1504,1605,1726,1860,1978,2117,2200,2301,2389,2485,2598,2720,2826,2965,3102,3235,3383,3505,3621,3741,3856,3945,4039,4158,4278,4374,4473,4577,4714,4857,4960,5057,5326,5485,6219,6370,6568,6914,6994,7091,7190,7285,7381,7464,7566,7662,7760,7894,7969,8066,8155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,643,745,840,943,1046,1148,6375", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "638,740,835,938,1041,1143,1257,6471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,978,1045,1126,1209,1279,1355,1429", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,973,1040,1121,1204,1274,1350,1424,1545"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1262,1351,5062,5157,5331,5490,5571,5677,5761,5842,5913,5980,6061,6224,6573,6649,6723", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "1346,1429,5152,5250,5411,5566,5672,5756,5837,5908,5975,6056,6139,6289,6644,6718,6839"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "589,687,789,889,988,1090,1199,6490", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "682,784,884,983,1085,1194,1311,6586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,983,1052,1140,1230,1303,1380,1447", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,978,1047,1135,1225,1298,1375,1442,1557"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1316,1413,5161,5258,5432,5594,5672,5759,5850,5932,6004,6073,6161,6333,6688,6765,6832", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "1408,1492,5253,5355,5515,5667,5754,5845,5927,5999,6068,6156,6246,6401,6760,6827,6942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,304,417,539,616,709,820,953,1069,1208,1288,1383,1474,1568,1684,1807,1907,2040,2171,2308,2480,2613,2728,2848,2971,3063,3155,3278,3415,3511,3612,3719,3854,3996,4104,4203,4275,4349,4431,4515,4612,4690,4769,4864,4964,5055,5155,5238,5345,5441,5550,5671,5749,5860", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "176,299,412,534,611,704,815,948,1064,1203,1283,1378,1469,1563,1679,1802,1902,2035,2166,2303,2475,2608,2723,2843,2966,3058,3150,3273,3410,3506,3607,3714,3849,3991,4099,4198,4270,4344,4426,4510,4607,4685,4764,4859,4959,5050,5150,5233,5340,5436,5545,5666,5744,5855,5955"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,231,354,467,1497,1574,1667,1778,1911,2027,2166,2246,2341,2432,2526,2642,2765,2865,2998,3129,3266,3438,3571,3686,3806,3929,4021,4113,4236,4373,4469,4570,4677,4812,4954,5062,5360,5520,6251,6406,6591,6947,7025,7104,7199,7299,7390,7490,7573,7680,7776,7885,8006,8084,8195", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "226,349,462,584,1569,1662,1773,1906,2022,2161,2241,2336,2427,2521,2637,2760,2860,2993,3124,3261,3433,3566,3681,3801,3924,4016,4108,4231,4368,4464,4565,4672,4807,4949,5057,5156,5427,5589,6328,6485,6683,7020,7099,7194,7294,7385,7485,7568,7675,7771,7880,8001,8079,8190,8290"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,268,372,475,550,636,740,864,970,1095,1173,1267,1350,1439,1545,1660,1759,1878,1999,2118,2262,2373,2481,2591,2702,2786,2877,2983,3098,3189,3287,3385,3500,3622,3723,3814,3884,3958,4038,4119,4217,4294,4373,4469,4562,4653,4744,4825,4922,5017,5111,5226,5304,5401", "endColumns": "106,105,103,102,74,85,103,123,105,124,77,93,82,88,105,114,98,118,120,118,143,110,107,109,110,83,90,105,114,90,97,97,114,121,100,90,69,73,79,80,97,76,78,95,92,90,90,80,96,94,93,114,77,96,92", "endOffsets": "157,263,367,470,545,631,735,859,965,1090,1168,1262,1345,1434,1540,1655,1754,1873,1994,2113,2257,2368,2476,2586,2697,2781,2872,2978,3093,3184,3282,3380,3495,3617,3718,3809,3879,3953,4033,4114,4212,4289,4368,4464,4557,4648,4739,4820,4917,5012,5106,5221,5299,5396,5489"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,422,1372,1447,1533,1637,1761,1867,1992,2070,2164,2247,2336,2442,2557,2656,2775,2896,3015,3159,3270,3378,3488,3599,3683,3774,3880,3995,4086,4184,4282,4397,4519,4620,4899,5051,5737,5887,6069,6428,6505,6584,6680,6773,6864,6955,7036,7133,7228,7322,7437,7515,7612", "endColumns": "106,105,103,102,74,85,103,123,105,124,77,93,82,88,105,114,98,118,120,118,143,110,107,109,110,83,90,105,114,90,97,97,114,121,100,90,69,73,79,80,97,76,78,95,92,90,90,80,96,94,93,114,77,96,92", "endOffsets": "207,313,417,520,1442,1528,1632,1756,1862,1987,2065,2159,2242,2331,2437,2552,2651,2770,2891,3010,3154,3265,3373,3483,3594,3678,3769,3875,3990,4081,4179,4277,4392,4514,4615,4706,4964,5120,5812,5963,6162,6500,6579,6675,6768,6859,6950,7031,7128,7223,7317,7432,7510,7607,7700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1212,1295,4711,4803,4969,5125,5203,5286,5368,5446,5512,5578,5656,5817,6167,6247,6312", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "1290,1367,4798,4894,5046,5198,5281,5363,5441,5507,5573,5651,5732,5882,6242,6307,6423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "525,618,718,815,914,1010,1112,5968", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "613,713,810,909,1005,1107,1207,6064"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1296,1391,5113,5210,5391,5551,5634,5731,5822,5909,5981,6050,6135,6310,6669,6745,6812", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "1386,1472,5205,5304,5472,5629,5726,5817,5904,5976,6045,6130,6220,6381,6740,6807,6920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,337,449,1477,1553,1645,1755,1885,1999,2146,2226,2324,2415,2511,2622,2748,2851,2986,3120,3256,3418,3550,3666,3787,3911,4003,4096,4212,4324,4420,4527,4632,4768,4909,5015,5309,5477,6225,6386,6572,6925,7001,7081,7178,7280,7368,7463,7547,7655,7752,7851,7966,8042,8138", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "215,332,444,556,1548,1640,1750,1880,1994,2141,2221,2319,2410,2506,2617,2743,2846,2981,3115,3251,3413,3545,3661,3782,3906,3998,4091,4207,4319,4415,4522,4627,4763,4904,5010,5108,5386,5546,6305,6466,6664,6996,7076,7173,7275,7363,7458,7542,7650,7747,7846,7961,8037,8133,8221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,658,760,859,959,1066,1176,6471", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "653,755,854,954,1061,1171,1291,6567"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,990,1060,1143,1230,1302,1384,1452", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,985,1055,1138,1225,1297,1379,1447,1567"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1295,1390,5076,5185,5362,5517,5594,5687,5777,5860,5931,6001,6084,6260,6626,6708,6776", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "1385,1470,5180,5285,5434,5589,5682,5772,5855,5926,5996,6079,6166,6327,6703,6771,6891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "558,655,757,855,954,1068,1173,6415", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "650,752,850,949,1063,1168,1290,6511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,279,397,508,585,680,793,927,1040,1173,1253,1348,1436,1528,1641,1761,1861,1997,2129,2269,2434,2556,2673,2795,2913,3002,3098,3214,3334,3429,3528,3630,3765,3908,4015,4109,4181,4259,4348,4431,4541,4617,4700,4799,4900,4987,5084,5168,5270,5365,5462,5576,5652,5751", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "161,274,392,503,580,675,788,922,1035,1168,1248,1343,1431,1523,1636,1756,1856,1992,2124,2264,2429,2551,2668,2790,2908,2997,3093,3209,3329,3424,3523,3625,3760,3903,4010,4104,4176,4254,4343,4426,4536,4612,4695,4794,4895,4982,5079,5163,5265,5360,5457,5571,5647,5746,5839"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,329,447,1475,1552,1647,1760,1894,2007,2140,2220,2315,2403,2495,2608,2728,2828,2964,3096,3236,3401,3523,3640,3762,3880,3969,4065,4181,4301,4396,4495,4597,4732,4875,4982,5290,5439,6171,6332,6516,6896,6972,7055,7154,7255,7342,7439,7523,7625,7720,7817,7931,8007,8106", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "211,324,442,553,1547,1642,1755,1889,2002,2135,2215,2310,2398,2490,2603,2723,2823,2959,3091,3231,3396,3518,3635,3757,3875,3964,4060,4176,4296,4391,4490,4592,4727,4870,4977,5071,5357,5512,6255,6410,6621,6967,7050,7149,7250,7337,7434,7518,7620,7715,7812,7926,8002,8101,8194"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "558,656,760,859,962,1068,1175,6476", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "651,755,854,957,1063,1170,1283,6572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,395,508,585,679,791,924,1037,1188,1269,1365,1453,1545,1661,1778,1879,2019,2150,2283,2451,2579,2701,2823,2948,3037,3133,3257,3395,3490,3587,3692,3827,3964,4070,4167,4241,4320,4402,4484,4586,4662,4743,4847,4945,5041,5135,5219,5321,5423,5520,5638,5714,5817", "endColumns": "113,114,110,112,76,93,111,132,112,150,80,95,87,91,115,116,100,139,130,132,167,127,121,121,124,88,95,123,137,94,96,104,134,136,105,96,73,78,81,81,101,75,80,103,97,95,93,83,101,101,96,117,75,102,95", "endOffsets": "164,279,390,503,580,674,786,919,1032,1183,1264,1360,1448,1540,1656,1773,1874,2014,2145,2278,2446,2574,2696,2818,2943,3032,3128,3252,3390,3485,3582,3687,3822,3959,4065,4162,4236,4315,4397,4479,4581,4657,4738,4842,4940,5036,5130,5214,5316,5418,5515,5633,5709,5812,5908"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,334,445,1465,1542,1636,1748,1881,1994,2145,2226,2322,2410,2502,2618,2735,2836,2976,3107,3240,3408,3536,3658,3780,3905,3994,4090,4214,4352,4447,4544,4649,4784,4921,5027,5334,5498,6236,6394,6577,6952,7028,7109,7213,7311,7407,7501,7585,7687,7789,7886,8004,8080,8183", "endColumns": "113,114,110,112,76,93,111,132,112,150,80,95,87,91,115,116,100,139,130,132,167,127,121,121,124,88,95,123,137,94,96,104,134,136,105,96,73,78,81,81,101,75,80,103,97,95,93,83,101,101,96,117,75,102,95", "endOffsets": "214,329,440,553,1537,1631,1743,1876,1989,2140,2221,2317,2405,2497,2613,2730,2831,2971,3102,3235,3403,3531,3653,3775,3900,3989,4085,4209,4347,4442,4539,4644,4779,4916,5022,5119,5403,5572,6313,6471,6674,7023,7104,7208,7306,7402,7496,7580,7682,7784,7881,7999,8075,8178,8274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,997,1065,1153,1241,1317,1396,1466", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,992,1060,1148,1236,1312,1391,1461,1585"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1288,1383,5124,5229,5408,5577,5659,5748,5841,5924,5992,6060,6148,6318,6679,6758,6828", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "1378,1460,5224,5329,5493,5654,5743,5836,5919,5987,6055,6143,6231,6389,6753,6823,6947"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\941abf8432552465cecf048cb40e7e68\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "6145", "endColumns": "53", "endOffsets": "6194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eed21004deab6640172f1382b4314917\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,77,78,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,105,117,118,119,120,121,122,123,195,232,233,237,238,242,244,245,253,259,269,302,332,365", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,630,695,761,830,893,963,1031,1103,1173,1234,1308,1381,1442,1503,1565,1629,1691,1752,1820,1920,1980,2046,2119,2188,2245,2297,2359,2431,2507,4886,4921,5183,5238,5301,5356,5414,5472,5533,5596,5653,5704,5754,5815,5872,5938,5972,6007,6366,7206,7273,7345,7414,7483,7557,7629,12306,14253,14370,14571,14681,14882,15101,15173,15544,15747,16048,17779,18779,19461", "endLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,77,78,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,105,117,118,119,120,121,122,123,195,232,236,237,241,242,244,245,258,268,301,322,364,370", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,690,756,825,888,958,1026,1098,1168,1229,1303,1376,1437,1498,1560,1624,1686,1747,1815,1915,1975,2041,2114,2183,2240,2292,2354,2426,2502,2567,4916,4951,5233,5296,5351,5409,5467,5528,5591,5648,5699,5749,5810,5867,5933,5967,6002,6037,6431,7268,7340,7409,7478,7552,7624,7712,12372,14365,14566,14676,14877,15006,15168,15235,15742,16043,17774,18455,19456,19623"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,8", "startColumns": "4,4", "startOffsets": "139,375", "endLines": "6,8", "endColumns": "12,90", "endOffsets": "369,461"}, "to": {"startLines": "218,243", "startColumns": "4,4", "startOffsets": "13706,15011", "endLines": "221,243", "endColumns": "12,89", "endOffsets": "13830,15096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c2a4308f293c798f5d1cd4bfb96df\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6700", "endColumns": "82", "endOffsets": "6778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2da7343ba5771349681ee327cfada024\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "79,246,399,402", "startColumns": "4,4,4,4", "startOffsets": "4956,15240,20891,21006", "endLines": "79,252,401,404", "endColumns": "52,24,24,24", "endOffsets": "5004,15539,21001,21116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e8f8ff59751c742e95202a21a7db5869\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6199", "endColumns": "49", "endOffsets": "6244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,104,126,127,165,166,169,172,173,175,177,178,179,182,183,188,201,202,203,222,225,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2572,2631,2690,2750,2810,2870,2930,2990,3050,3110,3170,3230,3290,3349,3409,3469,3529,3589,3649,3709,3769,3829,3889,3949,4008,4068,4128,4187,4246,4305,4364,4423,4482,4556,4614,4726,4777,6313,7819,7884,10584,10650,10837,10979,11031,11160,11281,11335,11371,11518,11568,11842,12654,12701,12737,13835,13947,14058", "endLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,104,126,127,165,166,169,172,173,175,177,178,179,182,183,188,201,202,203,224,227,231", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2626,2685,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3344,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4003,4063,4123,4182,4241,4300,4359,4418,4477,4551,4609,4664,4772,4827,6361,7879,7933,10645,10746,10890,11026,11086,11217,11330,11366,11400,11563,11617,11883,12696,12732,12822,13942,14053,14248"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "25,24,26,27,2,39,36,7,31,38,37,6,30,32,10,5,35,14,11,13,12,15,21,20,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1122,1057,1189,1252,55,1712,1590,264,1403,1670,1632,205,1352,1465,357,146,1552,636,427,564,494,718,976,917,822,871", "endColumns": "66,64,62,68,61,37,41,59,61,41,37,58,50,61,69,58,37,81,66,71,69,73,54,58,48,45", "endOffsets": "1184,1117,1247,1316,112,1745,1627,319,1460,1707,1665,259,1398,1522,422,200,1585,713,489,631,559,787,1026,971,866,912"}, "to": {"startLines": "106,107,108,109,111,112,124,125,164,167,170,176,180,181,184,185,186,190,191,192,193,194,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6436,6503,6568,6631,6783,6845,7717,7759,10522,10751,10895,11222,11405,11456,11622,11692,11751,11941,12023,12090,12162,12232,12377,12432,12491,12540", "endColumns": "66,64,62,68,61,37,41,59,61,41,37,58,50,61,69,58,37,81,66,71,69,73,54,58,48,45", "endOffsets": "6498,6563,6626,6695,6840,6878,7754,7814,10579,10788,10928,11276,11451,11513,11687,11746,11784,12018,12085,12157,12227,12301,12427,12486,12535,12581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9b41d762125eb57742305c4c67da3247\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "6042", "endColumns": "42", "endOffsets": "6080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36a284a0887b3c4c5321b113818582da\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "76,80", "startColumns": "4,4", "startOffsets": "4832,5009", "endColumns": "53,66", "endOffsets": "4881,5071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "113,114,115,116,128,129,130,131,132,133,136,137,138,139,140,141,142,143,144,145,146,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,168,171,174,187,189,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6883,6967,7049,7126,7938,7986,8047,8126,8228,8310,8426,8476,8541,8598,8663,8748,8839,8909,9002,9091,9185,9330,9417,9501,9593,9687,9747,9811,9894,9984,10047,10115,10183,10280,10385,10457,10793,10933,11091,11789,11888,12586,12827,12873,12923,12990,13057,13123,13188,13242,13314,13381,13451,13533,13579,13645", "endLines": "113,114,115,116,128,129,130,131,132,135,136,137,138,139,140,141,142,143,144,145,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,168,171,174,187,189,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "6962,7044,7121,7201,7981,8042,8121,8223,8305,8421,8471,8536,8593,8658,8743,8834,8904,8997,9086,9180,9325,9412,9496,9588,9682,9742,9806,9889,9979,10042,10110,10178,10275,10380,10452,10517,10832,10974,11155,11837,11936,12649,12868,12918,12985,13052,13118,13183,13237,13309,13376,13446,13528,13574,13640,13701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cc7a5eb8beabf3de963d3879e4678baf\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "371,384,390,396,405", "startColumns": "4,4,4,4,4", "startOffsets": "19628,20267,20511,20758,21121", "endLines": "383,389,395,398,409", "endColumns": "24,24,24,24,24", "endOffsets": "20262,20506,20753,20886,21298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c7b2283e135e78ac38f9cae36e3843b4\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "73,82,103,323,328", "startColumns": "4,4,4,4,4", "startOffsets": "4669,5118,6249,18460,18630", "endLines": "73,82,103,327,331", "endColumns": "56,64,63,24,24", "endOffsets": "4721,5178,6308,18625,18774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1111406b12509b34f86688129e1af7ed\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "81,100", "startColumns": "4,4", "startOffsets": "5076,6085", "endColumns": "41,59", "endOffsets": "5113,6140"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,986,1055,1138,1221,1296,1372,1438", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,981,1050,1133,1216,1291,1367,1433,1549"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1270,1362,5083,5176,5352,5517,5603,5704,5791,5877,5945,6014,6097,6261,6614,6690,6756", "endColumns": "91,81,92,98,86,85,100,86,85,67,68,82,82,74,75,65,115", "endOffsets": "1357,1439,5171,5270,5434,5598,5699,5786,5872,5940,6009,6092,6175,6331,6685,6751,6867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "552,646,749,846,948,1050,1148,6419", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "641,744,841,943,1045,1143,1265,6515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,394,502,581,676,789,924,1040,1178,1259,1359,1449,1545,1655,1779,1884,2015,2143,2269,2444,2566,2684,2807,2939,3030,3122,3245,3371,3468,3569,3672,3802,3939,4044,4141,4218,4296,4377,4460,4554,4630,4710,4807,4906,5000,5096,5179,5281,5376,5474,5588,5664,5760", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "160,268,389,497,576,671,784,919,1035,1173,1254,1354,1444,1540,1650,1774,1879,2010,2138,2264,2439,2561,2679,2802,2934,3025,3117,3240,3366,3463,3564,3667,3797,3934,4039,4136,4213,4291,4372,4455,4549,4625,4705,4802,4901,4995,5091,5174,5276,5371,5469,5583,5659,5755,5845"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,323,444,1444,1523,1618,1731,1866,1982,2120,2201,2301,2391,2487,2597,2721,2826,2957,3085,3211,3386,3508,3626,3749,3881,3972,4064,4187,4313,4410,4511,4614,4744,4881,4986,5275,5439,6180,6336,6520,6872,6948,7028,7125,7224,7318,7414,7497,7599,7694,7792,7906,7982,8078", "endColumns": "109,107,120,107,78,94,112,134,115,137,80,99,89,95,109,123,104,130,127,125,174,121,117,122,131,90,91,122,125,96,100,102,129,136,104,96,76,77,80,82,93,75,79,96,98,93,95,82,101,94,97,113,75,95,89", "endOffsets": "210,318,439,547,1518,1613,1726,1861,1977,2115,2196,2296,2386,2482,2592,2716,2821,2952,3080,3206,3381,3503,3621,3744,3876,3967,4059,4182,4308,4405,4506,4609,4739,4876,4981,5078,5347,5512,6256,6414,6609,6943,7023,7120,7219,7313,7409,7492,7594,7689,7787,7901,7977,8073,8163"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,283,395,510,589,686,801,935,1055,1193,1274,1373,1459,1552,1660,1777,1881,2018,2151,2278,2440,2562,2673,2789,2906,2993,3085,3200,3332,3430,3529,3631,3758,3892,3999,4093,4164,4247,4328,4412,4507,4583,4663,4759,4854,4945,5039,5121,5218,5312,5409,5520,5596,5694", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "163,278,390,505,584,681,796,930,1050,1188,1269,1368,1454,1547,1655,1772,1876,2013,2146,2273,2435,2557,2668,2784,2901,2988,3080,3195,3327,3425,3524,3626,3753,3887,3994,4088,4159,4242,4323,4407,4502,4578,4658,4754,4849,4940,5034,5116,5213,5307,5404,5515,5591,5689,5781"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,333,445,1469,1548,1645,1760,1894,2014,2152,2233,2332,2418,2511,2619,2736,2840,2977,3110,3237,3399,3521,3632,3748,3865,3952,4044,4159,4291,4389,4488,4590,4717,4851,4958,5243,5402,6130,6283,6468,6827,6903,6983,7079,7174,7265,7359,7441,7538,7632,7729,7840,7916,8014", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "213,328,440,555,1543,1640,1755,1889,2009,2147,2228,2327,2413,2506,2614,2731,2835,2972,3105,3232,3394,3516,3627,3743,3860,3947,4039,4154,4286,4384,4483,4585,4712,4846,4953,5047,5309,5480,6206,6362,6558,6898,6978,7074,7169,7260,7354,7436,7533,7627,7724,7835,7911,8009,8101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "560,658,760,863,964,1066,1164,6367", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "653,755,858,959,1061,1159,1288,6463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1293,1386,5052,5144,5314,5485,5563,5661,5749,5833,5901,5970,6049,6211,6563,6643,6709", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "1381,1464,5139,5238,5397,5558,5656,5744,5828,5896,5965,6044,6125,6278,6638,6704,6822"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1282,1371,4973,5072,5243,5405,5490,5581,5667,5747,5824,5899,5978,6142,6491,6572,6639", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "1366,1449,5067,5166,5320,5485,5576,5662,5742,5819,5894,5973,6055,6210,6567,6634,6752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,280,387,500,578,670,776,901,1011,1146,1226,1328,1415,1504,1614,1738,1845,1969,2091,2220,2389,2509,2622,2741,2859,2947,3038,3153,3270,3367,3466,3566,3692,3824,3927,4019,4091,4171,4253,4338,4428,4507,4586,4681,4777,4865,4962,5046,5149,5247,5354,5471,5549,5653", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "162,275,382,495,573,665,771,896,1006,1141,1221,1323,1410,1499,1609,1733,1840,1964,2086,2215,2384,2504,2617,2736,2854,2942,3033,3148,3265,3362,3461,3561,3687,3819,3922,4014,4086,4166,4248,4333,4423,4502,4581,4676,4772,4860,4957,5041,5144,5242,5349,5466,5544,5648,5743"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,330,437,1454,1532,1624,1730,1855,1965,2100,2180,2282,2369,2458,2568,2692,2799,2923,3045,3174,3343,3463,3576,3695,3813,3901,3992,4107,4224,4321,4420,4520,4646,4778,4881,5171,5325,6060,6215,6401,6757,6836,6915,7010,7106,7194,7291,7375,7478,7576,7683,7800,7878,7982", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "212,325,432,545,1527,1619,1725,1850,1960,2095,2175,2277,2364,2453,2563,2687,2794,2918,3040,3169,3338,3458,3571,3690,3808,3896,3987,4102,4219,4316,4415,4515,4641,4773,4876,4968,5238,5400,6137,6295,6486,6831,6910,7005,7101,7189,7286,7370,7473,7571,7678,7795,7873,7977,8072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "550,652,755,860,965,1064,1168,6300", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "647,750,855,960,1059,1163,1277,6396"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,420,535,609,701,811,937,1054,1197,1277,1372,1464,1556,1665,1790,1889,2024,2159,2300,2484,2620,2743,2870,3001,3094,3187,3320,3451,3546,3647,3748,3884,4026,4130,4228,4311,4388,4472,4558,4663,4739,4818,4914,5015,5107,5201,5285,5391,5487,5586,5701,5777,5883", "endColumns": "114,116,132,114,73,91,109,125,116,142,79,94,91,91,108,124,98,134,134,140,183,135,122,126,130,92,92,132,130,94,100,100,135,141,103,97,82,76,83,85,104,75,78,95,100,91,93,83,105,95,98,114,75,105,92", "endOffsets": "165,282,415,530,604,696,806,932,1049,1192,1272,1367,1459,1551,1660,1785,1884,2019,2154,2295,2479,2615,2738,2865,2996,3089,3182,3315,3446,3541,3642,3743,3879,4021,4125,4223,4306,4383,4467,4553,4658,4734,4813,4909,5010,5102,5196,5280,5386,5482,5581,5696,5772,5878,5971"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,337,470,1522,1596,1688,1798,1924,2041,2184,2264,2359,2451,2543,2652,2777,2876,3011,3146,3287,3471,3607,3730,3857,3988,4081,4174,4307,4438,4533,4634,4735,4871,5013,5117,5421,5588,6312,6468,6655,7033,7109,7188,7284,7385,7477,7571,7655,7761,7857,7956,8071,8147,8253", "endColumns": "114,116,132,114,73,91,109,125,116,142,79,94,91,91,108,124,98,134,134,140,183,135,122,126,130,92,92,132,130,94,100,100,135,141,103,97,82,76,83,85,104,75,78,95,100,91,93,83,105,95,98,114,75,105,92", "endOffsets": "215,332,465,580,1591,1683,1793,1919,2036,2179,2259,2354,2446,2538,2647,2772,2871,3006,3141,3282,3466,3602,3725,3852,3983,4076,4169,4302,4433,4528,4629,4730,4866,5008,5112,5210,5499,5660,6391,6549,6755,7104,7183,7279,7380,7472,7566,7650,7756,7852,7951,8066,8142,8248,8341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "585,682,792,894,995,1102,1207,6554", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "677,787,889,990,1097,1202,1321,6650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1000,1066,1150,1238,1310,1393,1462", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,995,1061,1145,1233,1305,1388,1457,1578"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1326,1429,5215,5318,5504,5665,5741,5832,5923,6007,6074,6140,6224,6396,6760,6843,6912", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "1424,1517,5313,5416,5583,5736,5827,5918,6002,6069,6135,6219,6307,6463,6838,6907,7028"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,990,1058,1141,1226,1299,1375,1445", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,985,1053,1136,1221,1294,1370,1440,1558"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1270,1363,5031,5136,5320,5482,5563,5656,5746,5828,5897,5965,6048,6215,6569,6645,6715", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "1358,1441,5131,5233,5402,5558,5651,5741,5823,5892,5960,6043,6128,6283,6640,6710,6828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "556,651,753,855,958,1062,1159,6368", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "646,748,850,953,1057,1154,1265,6464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,394,506,581,678,793,927,1048,1169,1249,1343,1433,1526,1638,1758,1862,2000,2136,2264,2419,2538,2649,2765,2877,2964,3058,3177,3308,3408,3515,3617,3751,3892,3996,4091,4173,4248,4330,4410,4510,4595,4676,4773,4873,4966,5058,5141,5241,5336,5429,5562,5648,5755", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "164,278,389,501,576,673,788,922,1043,1164,1244,1338,1428,1521,1633,1753,1857,1995,2131,2259,2414,2533,2644,2760,2872,2959,3053,3172,3303,3403,3510,3612,3746,3887,3991,4086,4168,4243,4325,4405,4505,4590,4671,4768,4868,4961,5053,5136,5236,5331,5424,5557,5643,5750,5847"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,333,444,1446,1521,1618,1733,1867,1988,2109,2189,2283,2373,2466,2578,2698,2802,2940,3076,3204,3359,3478,3589,3705,3817,3904,3998,4117,4248,4348,4455,4557,4691,4832,4936,5238,5407,6133,6288,6469,6833,6918,6999,7096,7196,7289,7381,7464,7564,7659,7752,7885,7971,8078", "endColumns": "113,113,110,111,74,96,114,133,120,120,79,93,89,92,111,119,103,137,135,127,154,118,110,115,111,86,93,118,130,99,106,101,133,140,103,94,81,74,81,79,99,84,80,96,99,92,91,82,99,94,92,132,85,106,96", "endOffsets": "214,328,439,551,1516,1613,1728,1862,1983,2104,2184,2278,2368,2461,2573,2693,2797,2935,3071,3199,3354,3473,3584,3700,3812,3899,3993,4112,4243,4343,4450,4552,4686,4827,4931,5026,5315,5477,6210,6363,6564,6913,6994,7091,7191,7284,7376,7459,7559,7654,7747,7880,7966,8073,8170"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1318,1414,5157,5255,5431,5595,5680,5772,5861,5949,6014,6078,6159,6326,6684,6763,6829", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "1409,1497,5250,5350,5513,5675,5767,5856,5944,6009,6073,6154,6238,6396,6758,6824,6944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,307,412,537,615,707,816,956,1070,1222,1303,1404,1495,1593,1707,1829,1930,2062,2195,2326,2501,2625,2744,2869,2991,3083,3177,3293,3418,3514,3615,3713,3849,3993,4095,4192,4268,4345,4428,4513,4610,4686,4768,4867,4969,5060,5157,5241,5346,5443,5542,5659,5735,5836", "endColumns": "126,124,104,124,77,91,108,139,113,151,80,100,90,97,113,121,100,131,132,130,174,123,118,124,121,91,93,115,124,95,100,97,135,143,101,96,75,76,82,84,96,75,81,98,101,90,96,83,104,96,98,116,75,100,91", "endOffsets": "177,302,407,532,610,702,811,951,1065,1217,1298,1399,1490,1588,1702,1824,1925,2057,2190,2321,2496,2620,2739,2864,2986,3078,3172,3288,3413,3509,3610,3708,3844,3988,4090,4187,4263,4340,4423,4508,4605,4681,4763,4862,4964,5055,5152,5236,5341,5438,5537,5654,5730,5831,5923"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,232,357,462,1502,1580,1672,1781,1921,2035,2187,2268,2369,2460,2558,2672,2794,2895,3027,3160,3291,3466,3590,3709,3834,3956,4048,4142,4258,4383,4479,4580,4678,4814,4958,5060,5355,5518,6243,6401,6587,6949,7025,7107,7206,7308,7399,7496,7580,7685,7782,7881,7998,8074,8175", "endColumns": "126,124,104,124,77,91,108,139,113,151,80,100,90,97,113,121,100,131,132,130,174,123,118,124,121,91,93,115,124,95,100,97,135,143,101,96,75,76,82,84,96,75,81,98,101,90,96,83,104,96,98,116,75,100,91", "endOffsets": "227,352,457,582,1575,1667,1776,1916,2030,2182,2263,2364,2455,2553,2667,2789,2890,3022,3155,3286,3461,3585,3704,3829,3951,4043,4137,4253,4378,4474,4575,4673,4809,4953,5055,5152,5426,5590,6321,6481,6679,7020,7102,7201,7303,7394,7491,7575,7680,7777,7876,7993,8069,8170,8262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "587,685,787,887,987,1095,1200,6486", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "680,782,882,982,1090,1195,1313,6582"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "651,749,851,950,1052,1156,1260,6652", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "744,846,945,1047,1151,1255,1369,6748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,339,457,601,676,766,875,1013,1128,1283,1363,1460,1557,1654,1771,1902,2004,2150,2292,2424,2622,2747,2861,2981,3111,3209,3310,3430,3554,3652,3750,3851,3991,4139,4244,4348,4422,4499,4585,4667,4770,4846,4927,5020,5127,5216,5316,5400,5512,5609,5713,5831,5907,6013", "endColumns": "142,140,117,143,74,89,108,137,114,154,79,96,96,96,116,130,101,145,141,131,197,124,113,119,129,97,100,119,123,97,97,100,139,147,104,103,73,76,85,81,102,75,80,92,106,88,99,83,111,96,103,117,75,105,92", "endOffsets": "193,334,452,596,671,761,870,1008,1123,1278,1358,1455,1552,1649,1766,1897,1999,2145,2287,2419,2617,2742,2856,2976,3106,3204,3305,3425,3549,3647,3745,3846,3986,4134,4239,4343,4417,4494,4580,4662,4765,4841,4922,5015,5122,5211,5311,5395,5507,5604,5708,5826,5902,6008,6101"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,248,389,507,1561,1636,1726,1835,1973,2088,2243,2323,2420,2517,2614,2731,2862,2964,3110,3252,3384,3582,3707,3821,3941,4071,4169,4270,4390,4514,4612,4710,4811,4951,5099,5204,5512,5673,6408,6570,6753,7126,7202,7283,7376,7483,7572,7672,7756,7868,7965,8069,8187,8263,8369", "endColumns": "142,140,117,143,74,89,108,137,114,154,79,96,96,96,116,130,101,145,141,131,197,124,113,119,129,97,100,119,123,97,97,100,139,147,104,103,73,76,85,81,102,75,80,92,106,88,99,83,111,96,103,117,75,105,92", "endOffsets": "243,384,502,646,1631,1721,1830,1968,2083,2238,2318,2415,2512,2609,2726,2857,2959,3105,3247,3379,3577,3702,3816,3936,4066,4164,4265,4385,4509,4607,4705,4806,4946,5094,5199,5303,5581,5745,6489,6647,6851,7197,7278,7371,7478,7567,7667,7751,7863,7960,8064,8182,8258,8364,8457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1374,1473,5308,5406,5586,5750,5830,5924,6016,6103,6174,6242,6323,6494,6856,6935,7004", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "1468,1556,5401,5507,5668,5825,5919,6011,6098,6169,6237,6318,6403,6565,6930,6999,7121"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1279,1383,5140,5236,5412,5574,5651,5741,5833,5917,5988,6058,6142,6319,6684,6765,6836", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "1378,1468,5231,5334,5492,5646,5736,5828,5912,5983,6053,6137,6226,6386,6760,6831,6952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,284,396,510,586,677,786,915,1032,1176,1257,1352,1442,1534,1645,1767,1867,2008,2148,2286,2454,2581,2698,2822,2942,3033,3127,3249,3380,3476,3574,3683,3821,3968,4080,4177,4250,4327,4415,4497,4607,4691,4770,4867,4965,5058,5151,5235,5338,5434,5531,5660,5742,5849", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "165,279,391,505,581,672,781,910,1027,1171,1252,1347,1437,1529,1640,1762,1862,2003,2143,2281,2449,2576,2693,2817,2937,3028,3122,3244,3375,3471,3569,3678,3816,3963,4075,4172,4245,4322,4410,4492,4602,4686,4765,4862,4960,5053,5146,5230,5333,5429,5526,5655,5737,5844,5943"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,334,446,1473,1549,1640,1749,1878,1995,2139,2220,2315,2405,2497,2608,2730,2830,2971,3111,3249,3417,3544,3661,3785,3905,3996,4090,4212,4343,4439,4537,4646,4784,4931,5043,5339,5497,6231,6391,6574,6957,7041,7120,7217,7315,7408,7501,7585,7688,7784,7881,8010,8092,8199", "endColumns": "114,113,111,113,75,90,108,128,116,143,80,94,89,91,110,121,99,140,139,137,167,126,116,123,119,90,93,121,130,95,97,108,137,146,111,96,72,76,87,81,109,83,78,96,97,92,92,83,102,95,96,128,81,106,98", "endOffsets": "215,329,441,555,1544,1635,1744,1873,1990,2134,2215,2310,2400,2492,2603,2725,2825,2966,3106,3244,3412,3539,3656,3780,3900,3991,4085,4207,4338,4434,4532,4641,4779,4926,5038,5135,5407,5569,6314,6468,6679,7036,7115,7212,7310,7403,7496,7580,7683,7779,7876,8005,8087,8194,8293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "560,658,760,857,955,1060,1163,6473", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "653,755,852,950,1055,1158,1274,6569"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,403,518,596,688,798,932,1046,1195,1275,1374,1466,1563,1677,1791,1895,2029,2162,2297,2476,2607,2722,2841,2965,3059,3152,3269,3413,3510,3619,3729,3863,4003,4109,4210,4292,4370,4452,4534,4636,4712,4792,4889,4991,5086,5181,5265,5372,5469,5567,5690,5766,5878", "endColumns": "115,112,118,114,77,91,109,133,113,148,79,98,91,96,113,113,103,133,132,134,178,130,114,118,123,93,92,116,143,96,108,109,133,139,105,100,81,77,81,81,101,75,79,96,101,94,94,83,106,96,97,122,75,111,103", "endOffsets": "166,279,398,513,591,683,793,927,1041,1190,1270,1369,1461,1558,1672,1786,1890,2024,2157,2292,2471,2602,2717,2836,2960,3054,3147,3264,3408,3505,3614,3724,3858,3998,4104,4205,4287,4365,4447,4529,4631,4707,4787,4884,4986,5081,5176,5260,5367,5464,5562,5685,5761,5873,5977"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,334,453,1475,1553,1645,1755,1889,2003,2152,2232,2331,2423,2520,2634,2748,2852,2986,3119,3254,3433,3564,3679,3798,3922,4016,4109,4226,4370,4467,4576,4686,4820,4960,5066,5377,5548,6296,6455,6638,7016,7092,7172,7269,7371,7466,7561,7645,7752,7849,7947,8070,8146,8258", "endColumns": "115,112,118,114,77,91,109,133,113,148,79,98,91,96,113,113,103,133,132,134,178,130,114,118,123,93,92,116,143,96,108,109,133,139,105,100,81,77,81,81,101,75,79,96,101,94,94,83,106,96,97,122,75,111,103", "endOffsets": "216,329,448,563,1548,1640,1750,1884,1998,2147,2227,2326,2418,2515,2629,2743,2847,2981,3114,3249,3428,3559,3674,3793,3917,4011,4104,4221,4365,4462,4571,4681,4815,4955,5061,5162,5454,5621,6373,6532,6735,7087,7167,7264,7366,7461,7556,7640,7747,7844,7942,8065,8141,8253,8357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "568,667,769,869,967,1074,1180,6537", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "662,764,864,962,1069,1175,1291,6633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,284,393,494,583,662,755,847,935,1008,1078,1163,1253,1330,1412,1484", "endColumns": "95,82,108,100,88,78,92,91,87,72,69,84,89,76,81,71,121", "endOffsets": "196,279,388,489,578,657,750,842,930,1003,1073,1158,1248,1325,1407,1479,1601"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1296,1392,5167,5276,5459,5626,5705,5798,5890,5978,6051,6121,6206,6378,6740,6822,6894", "endColumns": "95,82,108,100,88,78,92,91,87,72,69,84,89,76,81,71,121", "endOffsets": "1387,1470,5271,5372,5543,5700,5793,5885,5973,6046,6116,6201,6291,6450,6817,6889,7011"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1322,1415,5148,5258,5430,5601,5683,5781,5870,5955,6021,6088,6173,6342,6705,6784,6852", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "1410,1494,5253,5353,5510,5678,5776,5865,5950,6016,6083,6168,6255,6410,6779,6847,6965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "591,691,793,896,1003,1107,1211,6496", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "686,788,891,998,1102,1206,1317,6592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,287,420,541,622,716,828,960,1097,1249,1329,1423,1511,1604,1716,1831,1930,2062,2192,2325,2496,2621,2734,2851,2969,3058,3152,3270,3403,3502,3623,3724,3853,3988,4093,4190,4262,4348,4430,4511,4619,4704,4784,4880,4977,5069,5162,5245,5350,5445,5540,5684,5770,5885", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "169,282,415,536,617,711,823,955,1092,1244,1324,1418,1506,1599,1711,1826,1925,2057,2187,2320,2491,2616,2729,2846,2964,3053,3147,3265,3398,3497,3618,3719,3848,3983,4088,4185,4257,4343,4425,4506,4614,4699,4779,4875,4972,5064,5157,5240,5345,5440,5535,5679,5765,5880,5984"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,337,470,1499,1580,1674,1786,1918,2055,2207,2287,2381,2469,2562,2674,2789,2888,3020,3150,3283,3454,3579,3692,3809,3927,4016,4110,4228,4361,4460,4581,4682,4811,4946,5051,5358,5515,6260,6415,6597,6970,7055,7135,7231,7328,7420,7513,7596,7701,7796,7891,8035,8121,8236", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "219,332,465,586,1575,1669,1781,1913,2050,2202,2282,2376,2464,2557,2669,2784,2883,3015,3145,3278,3449,3574,3687,3804,3922,4011,4105,4223,4356,4455,4576,4677,4806,4941,5046,5143,5425,5596,6337,6491,6700,7050,7130,7226,7323,7415,7508,7591,7696,7791,7886,8030,8116,8231,8335"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,307,417,543,624,719,832,968,1076,1215,1295,1394,1484,1578,1690,1816,1920,2065,2207,2344,2536,2668,2780,2898,3035,3128,3223,3344,3468,3570,3672,3774,3912,4058,4162,4261,4333,4416,4506,4594,4697,4773,4852,4949,5050,5143,5241,5325,5432,5530,5627,5746,5822,5926", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "175,302,412,538,619,714,827,963,1071,1210,1290,1389,1479,1573,1685,1811,1915,2060,2202,2339,2531,2663,2775,2893,3030,3123,3218,3339,3463,3565,3667,3769,3907,4053,4157,4256,4328,4411,4501,4589,4692,4768,4847,4944,5045,5138,5236,5320,5427,5525,5622,5741,5817,5921,6014"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,230,357,467,1526,1607,1702,1815,1951,2059,2198,2278,2377,2467,2561,2673,2799,2903,3048,3190,3327,3519,3651,3763,3881,4018,4111,4206,4327,4451,4553,4655,4757,4895,5041,5145,5441,5602,6362,6529,6718,7084,7160,7239,7336,7437,7530,7628,7712,7819,7917,8014,8133,8209,8313", "endColumns": "124,126,109,125,80,94,112,135,107,138,79,98,89,93,111,125,103,144,141,136,191,131,111,117,136,92,94,120,123,101,101,101,137,145,103,98,71,82,89,87,102,75,78,96,100,92,97,83,106,97,96,118,75,103,92", "endOffsets": "225,352,462,588,1602,1697,1810,1946,2054,2193,2273,2372,2462,2556,2668,2794,2898,3043,3185,3322,3514,3646,3758,3876,4013,4106,4201,4322,4446,4548,4650,4752,4890,5036,5140,5239,5508,5680,6447,6612,6816,7155,7234,7331,7432,7525,7623,7707,7814,7912,8009,8128,8204,8308,8401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1343,1439,5244,5341,5513,5685,5774,5870,5958,6042,6115,6188,6272,6452,6821,6898,6967", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "1434,1521,5336,5436,5597,5769,5865,5953,6037,6110,6183,6267,6357,6524,6893,6962,7079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "593,695,803,905,1006,1112,1219,6617", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "690,798,900,1001,1107,1214,1338,6713"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "617,716,818,920,1023,1124,1226,6531", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "711,813,915,1018,1119,1221,1341,6627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,322,437,567,652,747,860,998,1115,1253,1334,1436,1526,1623,1748,1873,1980,2101,2220,2350,2528,2650,2766,2885,3015,3106,3197,3326,3469,3562,3663,3769,3891,4019,4128,4224,4302,4384,4471,4557,4656,4732,4813,4910,5010,5100,5201,5284,5386,5481,5584,5698,5774,5870", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "184,317,432,562,647,742,855,993,1110,1248,1329,1431,1521,1618,1743,1868,1975,2096,2215,2345,2523,2645,2761,2880,3010,3101,3192,3321,3464,3557,3658,3764,3886,4014,4123,4219,4297,4379,4466,4552,4651,4727,4808,4905,5005,5095,5196,5279,5381,5476,5579,5693,5769,5865,5955"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,239,372,487,1524,1609,1704,1817,1955,2072,2210,2291,2393,2483,2580,2705,2830,2937,3058,3177,3307,3485,3607,3723,3842,3972,4063,4154,4283,4426,4519,4620,4726,4848,4976,5085,5369,5533,6275,6445,6632,6992,7068,7149,7246,7346,7436,7537,7620,7722,7817,7920,8034,8110,8206", "endColumns": "133,132,114,129,84,94,112,137,116,137,80,101,89,96,124,124,106,120,118,129,177,121,115,118,129,90,90,128,142,92,100,105,121,127,108,95,77,81,86,85,98,75,80,96,99,89,100,82,101,94,102,113,75,95,89", "endOffsets": "234,367,482,612,1604,1699,1812,1950,2067,2205,2286,2388,2478,2575,2700,2825,2932,3053,3172,3302,3480,3602,3718,3837,3967,4058,4149,4278,4421,4514,4615,4721,4843,4971,5080,5176,5442,5610,6357,6526,6726,7063,7144,7241,7341,7431,7532,7615,7717,7812,7915,8029,8105,8201,8291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,988,1058,1136,1217,1300,1375,1443", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,983,1053,1131,1212,1295,1370,1438,1556"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1346,1440,5181,5271,5447,5615,5694,5800,5887,5976,6046,6116,6194,6362,6731,6806,6874", "endColumns": "93,83,89,97,85,78,105,86,88,69,69,77,80,82,74,67,117", "endOffsets": "1435,1519,5266,5364,5528,5689,5795,5882,5971,6041,6111,6189,6270,6440,6801,6869,6987"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,397,511,587,678,787,921,1033,1175,1255,1351,1439,1534,1648,1768,1869,2002,2132,2272,2457,2591,2710,2831,2954,3043,3135,3256,3393,3484,3587,3692,3826,3967,4074,4168,4241,4318,4400,4479,4580,4656,4735,4830,4927,5018,5112,5196,5301,5397,5495,5619,5695,5805", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "165,277,392,506,582,673,782,916,1028,1170,1250,1346,1434,1529,1643,1763,1864,1997,2127,2267,2452,2586,2705,2826,2949,3038,3130,3251,3388,3479,3582,3687,3821,3962,4069,4163,4236,4313,4395,4474,4575,4651,4730,4825,4922,5013,5107,5191,5296,5392,5490,5614,5690,5800,5903"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,332,447,1476,1552,1643,1752,1886,1998,2140,2220,2316,2404,2499,2613,2733,2834,2967,3097,3237,3422,3556,3675,3796,3919,4008,4100,4221,4358,4449,4552,4657,4791,4932,5039,5334,5503,6250,6405,6585,6950,7026,7105,7200,7297,7388,7482,7566,7671,7767,7865,7989,8065,8175", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "215,327,442,556,1547,1638,1747,1881,1993,2135,2215,2311,2399,2494,2608,2728,2829,2962,3092,3232,3417,3551,3670,3791,3914,4003,4095,4216,4353,4444,4547,4652,4786,4927,5034,5128,5402,5575,6327,6479,6681,7021,7100,7195,7292,7383,7477,7561,7666,7762,7860,7984,8060,8170,8273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,660,762,860,957,1065,1176,6484", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "655,757,855,952,1060,1171,1293,6580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1298,1393,5133,5232,5407,5580,5661,5754,5846,5936,6005,6072,6159,6332,6686,6763,6829", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "1388,1471,5227,5329,5498,5656,5749,5841,5931,6000,6067,6154,6245,6400,6758,6824,6945"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,256,358,458,529,614,717,832,938,1056,1134,1227,1308,1394,1499,1609,1707,1816,1923,2031,2160,2265,2365,2469,2575,2657,2748,2853,2958,3045,3139,3232,3341,3456,3551,3638,3708,3778,3857,3935,4026,4102,4180,4273,4364,4455,4548,4627,4719,4809,4902,5015,5091,5181", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "151,251,353,453,524,609,712,827,933,1051,1129,1222,1303,1389,1494,1604,1702,1811,1918,2026,2155,2260,2360,2464,2570,2652,2743,2848,2953,3040,3134,3227,3336,3451,3546,3633,3703,3773,3852,3930,4021,4097,4175,4268,4359,4450,4543,4622,4714,4804,4897,5010,5086,5176,5263"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,306,408,1340,1411,1496,1599,1714,1820,1938,2016,2109,2190,2276,2381,2491,2589,2698,2805,2913,3042,3147,3247,3351,3457,3539,3630,3735,3840,3927,4021,4114,4223,4338,4433,4698,4848,5507,5654,5833,6176,6252,6330,6423,6514,6605,6698,6777,6869,6959,7052,7165,7241,7331", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "201,301,403,503,1406,1491,1594,1709,1815,1933,2011,2104,2185,2271,2376,2486,2584,2693,2800,2908,3037,3142,3242,3346,3452,3534,3625,3730,3835,3922,4016,4109,4218,4333,4428,4515,4763,4913,5581,5727,5919,6247,6325,6418,6509,6600,6693,6772,6864,6954,7047,7160,7236,7326,7413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,894,958,1031,1107,1175,1249,1313", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,889,953,1026,1102,1170,1244,1308,1422"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1185,1264,4520,4608,4768,4918,4993,5072,5151,5230,5294,5358,5431,5586,5924,5998,6062", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "1259,1335,4603,4693,4843,4988,5067,5146,5225,5289,5353,5426,5502,5649,5993,6057,6171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "508,600,700,794,891,987,1085,5732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "595,695,789,886,982,1080,1180,5828"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,397,511,587,678,787,917,1029,1160,1241,1337,1425,1518,1630,1748,1849,1978,2104,2238,2397,2521,2634,2755,2873,2962,3055,3168,3279,3373,3472,3576,3703,3840,3946,4040,4120,4197,4280,4362,4456,4532,4614,4711,4810,4903,5000,5084,5186,5281,5379,5494,5570,5670", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "164,277,392,506,582,673,782,912,1024,1155,1236,1332,1420,1513,1625,1743,1844,1973,2099,2233,2392,2516,2629,2750,2868,2957,3050,3163,3274,3368,3467,3571,3698,3835,3941,4035,4115,4192,4275,4357,4451,4527,4609,4706,4805,4898,4995,5079,5181,5276,5374,5489,5565,5665,5756"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,332,447,1468,1544,1635,1744,1874,1986,2117,2198,2294,2382,2475,2587,2705,2806,2935,3061,3195,3354,3478,3591,3712,3830,3919,4012,4125,4236,4330,4429,4533,4660,4797,4903,5193,5365,6090,6245,6428,6789,6865,6947,7044,7143,7236,7333,7417,7519,7614,7712,7827,7903,8003", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "214,327,442,556,1539,1630,1739,1869,1981,2112,2193,2289,2377,2470,2582,2700,2801,2930,3056,3190,3349,3473,3586,3707,3825,3914,4007,4120,4231,4325,4424,4528,4655,4792,4898,4992,5268,5437,6168,6322,6517,6860,6942,7039,7138,7231,7328,7412,7514,7609,7707,7822,7898,7998,8089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,980,1049,1131,1217,1289,1368,1436", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,975,1044,1126,1212,1284,1363,1431,1551"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1292,1385,4997,5091,5273,5442,5520,5612,5703,5784,5853,5922,6004,6173,6522,6601,6669", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "1380,1463,5086,5188,5360,5515,5607,5698,5779,5848,5917,5999,6085,6240,6596,6664,6784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,659,761,862,961,1066,1173,6327", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "654,756,857,956,1061,1168,1287,6423"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1383,5063,5160,5334,5496,5572,5663,5753,5839,5903,5968,6046,6208,6565,6646,6716", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "1378,1465,5155,5256,5415,5567,5658,5748,5834,5898,5963,6041,6122,6274,6641,6711,6831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,282,393,505,581,681,799,936,1060,1210,1291,1386,1472,1571,1682,1799,1899,2023,2144,2272,2434,2555,2673,2793,2919,3006,3101,3213,3335,3431,3536,3635,3767,3903,4005,4098,4171,4247,4328,4412,4513,4590,4669,4764,4858,4949,5043,5127,5226,5322,5420,5532,5609,5705", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "163,277,388,500,576,676,794,931,1055,1205,1286,1381,1467,1566,1677,1794,1894,2018,2139,2267,2429,2550,2668,2788,2914,3001,3096,3208,3330,3426,3531,3630,3762,3898,4000,4093,4166,4242,4323,4407,4508,4585,4664,4759,4853,4944,5038,5122,5221,5317,5415,5527,5604,5700,5792"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,332,443,1470,1546,1646,1764,1901,2025,2175,2256,2351,2437,2536,2647,2764,2864,2988,3109,3237,3399,3520,3638,3758,3884,3971,4066,4178,4300,4396,4501,4600,4732,4868,4970,5261,5420,6127,6279,6464,6836,6913,6992,7087,7181,7272,7366,7450,7549,7645,7743,7855,7932,8028", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "213,327,438,550,1541,1641,1759,1896,2020,2170,2251,2346,2432,2531,2642,2759,2859,2983,3104,3232,3394,3515,3633,3753,3879,3966,4061,4173,4295,4391,4496,4595,4727,4863,4965,5058,5329,5491,6203,6358,6560,6908,6987,7082,7176,7267,7361,7445,7544,7640,7738,7850,7927,8023,8115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "555,653,755,853,951,1058,1167,6363", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "648,750,848,946,1053,1162,1282,6459"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1015,1079,1163,1251,1336,1414,1483", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1010,1074,1158,1246,1331,1409,1478,1599"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1333,1433,5236,5334,5519,5691,5770,5876,5969,6064,6129,6193,6277,6447,6814,6892,6961", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "1428,1515,5329,5429,5601,5765,5871,5964,6059,6124,6188,6272,6360,6527,6887,6956,7077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,298,416,536,624,716,826,965,1080,1234,1314,1412,1502,1598,1713,1831,1944,2082,2218,2357,2529,2660,2776,2896,3023,3114,3207,3330,3464,3560,3666,3768,3906,4050,4156,4252,4337,4422,4504,4586,4685,4761,4840,4935,5034,5121,5215,5299,5405,5501,5599,5713,5789,5899", "endColumns": "122,119,117,119,87,91,109,138,114,153,79,97,89,95,114,117,112,137,135,138,171,130,115,119,126,90,92,122,133,95,105,101,137,143,105,95,84,84,81,81,98,75,78,94,98,86,93,83,105,95,97,113,75,109,102", "endOffsets": "173,293,411,531,619,711,821,960,1075,1229,1309,1407,1497,1593,1708,1826,1939,2077,2213,2352,2524,2655,2771,2891,3018,3109,3202,3325,3459,3555,3661,3763,3901,4045,4151,4247,4332,4417,4499,4581,4680,4756,4835,4930,5029,5116,5210,5294,5400,5496,5594,5708,5784,5894,5997"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,348,466,1520,1608,1700,1810,1949,2064,2218,2298,2396,2486,2582,2697,2815,2928,3066,3202,3341,3513,3644,3760,3880,4007,4098,4191,4314,4448,4544,4650,4752,4890,5034,5140,5434,5606,6365,6532,6715,7082,7158,7237,7332,7431,7518,7612,7696,7802,7898,7996,8110,8186,8296", "endColumns": "122,119,117,119,87,91,109,138,114,153,79,97,89,95,114,117,112,137,135,138,171,130,115,119,126,90,92,122,133,95,105,101,137,143,105,95,84,84,81,81,98,75,78,94,98,86,93,83,105,95,97,113,75,109,102", "endOffsets": "223,343,461,581,1603,1695,1805,1944,2059,2213,2293,2391,2481,2577,2692,2810,2923,3061,3197,3336,3508,3639,3755,3875,4002,4093,4186,4309,4443,4539,4645,4747,4885,5029,5135,5231,5514,5686,6442,6609,6809,7153,7232,7327,7426,7513,7607,7691,7797,7893,7991,8105,8181,8291,8394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "586,684,786,885,987,1096,1203,6614", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "679,781,880,982,1091,1198,1328,6710"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "559,657,759,860,961,1066,1169,6395", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "652,754,855,956,1061,1164,1281,6491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1286,1379,5045,5143,5326,5496,5578,5668,5756,5838,5909,5979,6063,6240,6600,6684,6754", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "1374,1457,5138,5240,5413,5573,5663,5751,5833,5904,5974,6058,6145,6307,6679,6749,6872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,679,790,927,1040,1176,1256,1350,1439,1533,1644,1763,1869,1994,2118,2240,2416,2536,2655,2779,2896,2983,3079,3196,3325,3419,3529,3633,3760,3894,3997,4092,4173,4251,4341,4424,4528,4604,4684,4778,4875,4965,5056,5140,5242,5336,5430,5573,5649,5751", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "164,276,390,504,581,674,785,922,1035,1171,1251,1345,1434,1528,1639,1758,1864,1989,2113,2235,2411,2531,2650,2774,2891,2978,3074,3191,3320,3414,3524,3628,3755,3889,3992,4087,4168,4246,4336,4419,4523,4599,4679,4773,4870,4960,5051,5135,5237,5331,5425,5568,5644,5746,5839"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,445,1462,1539,1632,1743,1880,1993,2129,2209,2303,2392,2486,2597,2716,2822,2947,3071,3193,3369,3489,3608,3732,3849,3936,4032,4149,4278,4372,4482,4586,4713,4847,4950,5245,5418,6150,6312,6496,6877,6953,7033,7127,7224,7314,7405,7489,7591,7685,7779,7922,7998,8100", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "214,326,440,554,1534,1627,1738,1875,1988,2124,2204,2298,2387,2481,2592,2711,2817,2942,3066,3188,3364,3484,3603,3727,3844,3931,4027,4144,4273,4367,4477,4581,4708,4842,4945,5040,5321,5491,6235,6390,6595,6948,7028,7122,7219,7309,7400,7484,7586,7680,7774,7917,7993,8095,8188"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "563,661,771,870,973,1084,1194,6487", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "656,766,865,968,1079,1189,1309,6583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,399,513,591,681,789,917,1029,1169,1249,1344,1436,1531,1652,1766,1866,2001,2132,2267,2459,2585,2699,2822,2946,3039,3136,3254,3379,3473,3572,3675,3808,3952,4057,4156,4236,4314,4398,4484,4591,4674,4757,4853,4958,5050,5145,5229,5336,5428,5523,5657,5737,5836", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "165,281,394,508,586,676,784,912,1024,1164,1244,1339,1431,1526,1647,1761,1861,1996,2127,2262,2454,2580,2694,2817,2941,3034,3131,3249,3374,3468,3567,3670,3803,3947,4052,4151,4231,4309,4393,4479,4586,4669,4752,4848,4953,5045,5140,5224,5331,5423,5518,5652,5732,5831,5924"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,336,449,1491,1569,1659,1767,1895,2007,2147,2227,2322,2414,2509,2630,2744,2844,2979,3110,3245,3437,3563,3677,3800,3924,4017,4114,4232,4357,4451,4550,4653,4786,4930,5035,5337,5512,6242,6401,6588,6980,7063,7146,7242,7347,7439,7534,7618,7725,7817,7912,8046,8126,8225", "endColumns": "114,115,112,113,77,89,107,127,111,139,79,94,91,94,120,113,99,134,130,134,191,125,113,122,123,92,96,117,124,93,98,102,132,143,104,98,79,77,83,85,106,82,82,95,104,91,94,83,106,91,94,133,79,98,92", "endOffsets": "215,331,444,558,1564,1654,1762,1890,2002,2142,2222,2317,2409,2504,2625,2739,2839,2974,3105,3240,3432,3558,3672,3795,3919,4012,4109,4227,4352,4446,4545,4648,4781,4925,5030,5129,5412,5585,6321,6482,6690,7058,7141,7237,7342,7434,7529,7613,7720,7812,7907,8041,8121,8220,8313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,989,1058,1144,1232,1307,1387,1470", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,984,1053,1139,1227,1302,1382,1465,1587"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1314,1407,5134,5232,5417,5590,5667,5758,5845,5929,5999,6068,6154,6326,6695,6775,6858", "endColumns": "92,83,97,104,94,76,90,86,83,69,68,85,87,74,79,82,121", "endOffsets": "1402,1486,5227,5332,5507,5662,5753,5840,5924,5994,6063,6149,6237,6396,6770,6853,6975"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1269,1354,5077,5181,5351,5517,5601,5684,5769,5856,5921,5986,6066,6235,6593,6677,6743", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "1349,1429,5176,5274,5434,5596,5679,5764,5851,5916,5981,6061,6146,6306,6672,6738,6856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,643,746,844,944,1045,1157,6395", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "638,741,839,939,1040,1152,1264,6491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,386,498,577,679,799,939,1063,1211,1298,1394,1490,1596,1716,1837,1938,2060,2182,2306,2464,2582,2692,2802,2922,3019,3115,3237,3372,3472,3573,3681,3801,3925,4038,4141,4213,4291,4375,4459,4556,4632,4712,4808,4906,4998,5103,5186,5284,5378,5484,5597,5673,5776", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "164,279,381,493,572,674,794,934,1058,1206,1293,1389,1485,1591,1711,1832,1933,2055,2177,2301,2459,2577,2687,2797,2917,3014,3110,3232,3367,3467,3568,3676,3796,3920,4033,4136,4208,4286,4370,4454,4551,4627,4707,4803,4901,4993,5098,5181,5279,5373,5479,5592,5668,5771,5867"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,334,436,1434,1513,1615,1735,1875,1999,2147,2234,2330,2426,2532,2652,2773,2874,2996,3118,3242,3400,3518,3628,3738,3858,3955,4051,4173,4308,4408,4509,4617,4737,4861,4974,5279,5439,6151,6311,6496,6861,6937,7017,7113,7211,7303,7408,7491,7589,7683,7789,7902,7978,8081", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "214,329,431,543,1508,1610,1730,1870,1994,2142,2229,2325,2421,2527,2647,2768,2869,2991,3113,3237,3395,3513,3623,3733,3853,3950,4046,4168,4303,4403,4504,4612,4732,4856,4969,5072,5346,5512,6230,6390,6588,6932,7012,7108,7206,7298,7403,7486,7584,7678,7784,7897,7973,8076,8172"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1163,1240,4411,4498,4659,4808,4882,4959,5037,5112,5177,5242,5315,5469,5801,5875,5943", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "1235,1311,4493,4584,4732,4877,4954,5032,5107,5172,5237,5310,5385,5532,5870,5938,6054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,608,709,810,911,1023,1101,1193,1273,1357,1458,1567,1664,1770,1874,1978,2090,2191,2288,2389,2491,2572,2663,2764,2869,2955,3053,3147,3253,3365,3461,3547,3617,3688,3767,3845,3930,4006,4084,4177,4267,4356,4445,4525,4617,4709,4799,4903,4979,5069", "endColumns": "99,98,99,97,71,83,100,100,100,111,77,91,79,83,100,108,96,105,103,103,111,100,96,100,101,80,90,100,104,85,97,93,105,111,95,85,69,70,78,77,84,75,77,92,89,88,88,79,91,91,89,103,75,89,87", "endOffsets": "150,249,349,447,519,603,704,805,906,1018,1096,1188,1268,1352,1453,1562,1659,1765,1869,1973,2085,2186,2283,2384,2486,2567,2658,2759,2864,2950,3048,3142,3248,3360,3456,3542,3612,3683,3762,3840,3925,4001,4079,4172,4262,4351,4440,4520,4612,4704,4794,4898,4974,5064,5152"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,304,404,1316,1388,1472,1573,1674,1775,1887,1965,2057,2137,2221,2322,2431,2528,2634,2738,2842,2954,3055,3152,3253,3355,3436,3527,3628,3733,3819,3917,4011,4117,4229,4325,4589,4737,5390,5537,5716,6059,6135,6213,6306,6396,6485,6574,6654,6746,6838,6928,7032,7108,7198", "endColumns": "99,98,99,97,71,83,100,100,100,111,77,91,79,83,100,108,96,105,103,103,111,100,96,100,101,80,90,100,104,85,97,93,105,111,95,85,69,70,78,77,84,75,77,92,89,88,88,79,91,91,89,103,75,89,87", "endOffsets": "200,299,399,497,1383,1467,1568,1669,1770,1882,1960,2052,2132,2216,2317,2426,2523,2629,2733,2837,2949,3050,3147,3248,3350,3431,3522,3623,3728,3814,3912,4006,4112,4224,4320,4406,4654,4803,5464,5610,5796,6130,6208,6301,6391,6480,6569,6649,6741,6833,6923,7027,7103,7193,7281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,594,693,787,881,974,1067,5615", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "589,688,782,876,969,1062,1158,5711"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "574,671,773,874,971,1078,1186,6594", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "666,768,869,966,1073,1181,1303,6690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,407,524,606,702,816,959,1082,1228,1309,1404,1495,1589,1706,1840,1940,2083,2227,2370,2536,2670,2789,2911,3033,3125,3220,3341,3472,3575,3675,3784,3924,4069,4181,4282,4354,4433,4518,4603,4706,4782,4862,4958,5058,5154,5256,5340,5448,5549,5654,5769,5845,5948", "endColumns": "119,117,113,116,81,95,113,142,122,145,80,94,90,93,116,133,99,142,143,142,165,133,118,121,121,91,94,120,130,102,99,108,139,144,111,100,71,78,84,84,102,75,79,95,99,95,101,83,107,100,104,114,75,102,90", "endOffsets": "170,288,402,519,601,697,811,954,1077,1223,1304,1399,1490,1584,1701,1835,1935,2078,2222,2365,2531,2665,2784,2906,3028,3120,3215,3336,3467,3570,3670,3779,3919,4064,4176,4277,4349,4428,4513,4598,4701,4777,4857,4953,5053,5149,5251,5335,5443,5544,5649,5764,5840,5943,6034"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,343,457,1493,1575,1671,1785,1928,2051,2197,2278,2373,2464,2558,2675,2809,2909,3052,3196,3339,3505,3639,3758,3880,4002,4094,4189,4310,4441,4544,4644,4753,4893,5038,5150,5450,5612,6353,6509,6695,7069,7145,7225,7321,7421,7517,7619,7703,7811,7912,8017,8132,8208,8311", "endColumns": "119,117,113,116,81,95,113,142,122,145,80,94,90,93,116,133,99,142,143,142,165,133,118,121,121,91,94,120,130,102,99,108,139,144,111,100,71,78,84,84,102,75,79,95,99,95,101,83,107,100,104,114,75,102,90", "endOffsets": "220,338,452,569,1570,1666,1780,1923,2046,2192,2273,2368,2459,2553,2670,2804,2904,3047,3191,3334,3500,3634,3753,3875,3997,4089,4184,4305,4436,4539,4639,4748,4888,5033,5145,5246,5517,5686,6433,6589,6793,7140,7220,7316,7416,7512,7614,7698,7806,7907,8012,8127,8203,8306,8397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,999,1068,1155,1241,1312,1390,1456", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,994,1063,1150,1236,1307,1385,1451,1578"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1308,1407,5251,5348,5522,5691,5773,5865,5957,6041,6111,6180,6267,6438,6798,6876,6942", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "1402,1488,5343,5445,5607,5768,5860,5952,6036,6106,6175,6262,6348,6504,6871,6937,7064"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "559,655,757,856,955,1061,1165,6442", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "650,752,851,950,1056,1160,1278,6538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1003,1072,1161,1252,1324,1403,1473", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,998,1067,1156,1247,1319,1398,1468,1589"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1283,1379,5086,5185,5361,5532,5612,5708,5798,5885,5956,6025,6114,6287,6652,6731,6801", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "1374,1460,5180,5283,5446,5607,5703,5793,5880,5951,6020,6109,6200,6354,6726,6796,6917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,399,509,586,681,794,926,1040,1184,1266,1364,1454,1548,1666,1782,1885,2016,2149,2280,2448,2573,2686,2801,2919,3010,3103,3217,3352,3451,3549,3656,3789,3925,4032,4130,4203,4284,4366,4449,4558,4634,4715,4812,4911,5001,5099,5181,5283,5375,5478,5591,5667,5769", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "164,277,394,504,581,676,789,921,1035,1179,1261,1359,1449,1543,1661,1777,1880,2011,2144,2275,2443,2568,2681,2796,2914,3005,3098,3212,3347,3446,3544,3651,3784,3920,4027,4125,4198,4279,4361,4444,4553,4629,4710,4807,4906,4996,5094,5176,5278,5370,5473,5586,5662,5764,5857"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,332,449,1465,1542,1637,1750,1882,1996,2140,2222,2320,2410,2504,2622,2738,2841,2972,3105,3236,3404,3529,3642,3757,3875,3966,4059,4173,4308,4407,4505,4612,4745,4881,4988,5288,5451,6205,6359,6543,6922,6998,7079,7176,7275,7365,7463,7545,7647,7739,7842,7955,8031,8133", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "214,327,444,554,1537,1632,1745,1877,1991,2135,2217,2315,2405,2499,2617,2733,2836,2967,3100,3231,3399,3524,3637,3752,3870,3961,4054,4168,4303,4402,4500,4607,4740,4876,4983,5081,5356,5527,6282,6437,6647,6993,7074,7171,7270,7360,7458,7540,7642,7734,7837,7950,8026,8128,8221"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1305,1399,5091,5188,5358,5527,5613,5698,5787,5870,5939,6009,6089,6255,6609,6685,6751", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "1394,1476,5183,5281,5440,5608,5693,5782,5865,5934,6004,6084,6169,6321,6680,6746,6864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,402,523,604,700,812,944,1063,1205,1286,1388,1475,1569,1676,1802,1909,2042,2171,2295,2470,2591,2703,2819,2939,3027,3118,3234,3359,3455,3554,3659,3791,3929,4040,4133,4205,4287,4368,4454,4550,4626,4705,4800,4895,4988,5083,5166,5266,5362,5461,5575,5651,5747", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "171,290,397,518,599,695,807,939,1058,1200,1281,1383,1470,1564,1671,1797,1904,2037,2166,2290,2465,2586,2698,2814,2934,3022,3113,3229,3354,3450,3549,3654,3786,3924,4035,4128,4200,4282,4363,4449,4545,4621,4700,4795,4890,4983,5078,5161,5261,5357,5456,5570,5646,5742,5832"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,345,452,1481,1562,1658,1770,1902,2021,2163,2244,2346,2433,2527,2634,2760,2867,3000,3129,3253,3428,3549,3661,3777,3897,3985,4076,4192,4317,4413,4512,4617,4749,4887,4998,5286,5445,6174,6326,6513,6869,6945,7024,7119,7214,7307,7402,7485,7585,7681,7780,7894,7970,8066", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "221,340,447,568,1557,1653,1765,1897,2016,2158,2239,2341,2428,2522,2629,2755,2862,2995,3124,3248,3423,3544,3656,3772,3892,3980,4071,4187,4312,4408,4507,4612,4744,4882,4993,5086,5353,5522,6250,6407,6604,6940,7019,7114,7209,7302,7397,7480,7580,7676,7775,7889,7965,8061,8151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,673,777,878,981,1083,1188,6412", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "668,772,873,976,1078,1183,1300,6508"}}]}, {"outputFile": "com.codex.aijourney.app-merged_res-63:/values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1305,1401,5150,5248,5434,5601,5680,5773,5865,5952,6025,6095,6181,6355,6722,6804,6874", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "1396,1478,5243,5346,5518,5675,5768,5860,5947,6020,6090,6176,6267,6427,6799,6869,6990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,601,697,811,950,1064,1208,1289,1387,1480,1578,1685,1799,1902,2036,2165,2305,2478,2598,2714,2833,2960,3054,3146,3263,3394,3493,3603,3714,3846,3983,4090,4190,4273,4351,4434,4516,4623,4699,4779,4876,4978,5074,5169,5254,5361,5458,5557,5672,5748,5861", "endColumns": "116,114,118,116,77,95,113,138,113,143,80,97,92,97,106,113,102,133,128,139,172,119,115,118,126,93,91,116,130,98,109,110,131,136,106,99,82,77,82,81,106,75,79,96,101,95,94,84,106,96,98,114,75,112,104", "endOffsets": "167,282,401,518,596,692,806,945,1059,1203,1284,1382,1475,1573,1680,1794,1897,2031,2160,2300,2473,2593,2709,2828,2955,3049,3141,3258,3389,3488,3598,3709,3841,3978,4085,4185,4268,4346,4429,4511,4618,4694,4774,4871,4973,5069,5164,5249,5356,5453,5552,5667,5743,5856,5961"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,337,456,1483,1561,1657,1771,1910,2024,2168,2249,2347,2440,2538,2645,2759,2862,2996,3125,3265,3438,3558,3674,3793,3920,4014,4106,4223,4354,4453,4563,4674,4806,4943,5050,5351,5523,6272,6432,6615,6995,7071,7151,7248,7350,7446,7541,7626,7733,7830,7929,8044,8120,8233", "endColumns": "116,114,118,116,77,95,113,138,113,143,80,97,92,97,106,113,102,133,128,139,172,119,115,118,126,93,91,116,130,98,109,110,131,136,106,99,82,77,82,81,106,75,79,96,101,95,94,84,106,96,98,114,75,112,104", "endOffsets": "217,332,451,568,1556,1652,1766,1905,2019,2163,2244,2342,2435,2533,2640,2754,2857,2991,3120,3260,3433,3553,3669,3788,3915,4009,4101,4218,4349,4448,4558,4669,4801,4938,5045,5145,5429,5596,6350,6509,6717,7066,7146,7243,7345,7441,7536,7621,7728,7825,7924,8039,8115,8228,8333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "573,672,774,874,972,1079,1185,6514", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "667,769,869,967,1074,1180,1300,6610"}}]}]}