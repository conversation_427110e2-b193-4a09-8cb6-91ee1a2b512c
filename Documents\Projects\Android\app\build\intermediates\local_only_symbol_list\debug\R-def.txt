R_DEF: Internal format may change without notice
local
color black
color md_theme_dark_background
color md_theme_dark_error
color md_theme_dark_errorContainer
color md_theme_dark_onBackground
color md_theme_dark_onError
color md_theme_dark_onErrorContainer
color md_theme_dark_onPrimary
color md_theme_dark_onPrimaryContainer
color md_theme_dark_onSecondary
color md_theme_dark_onSecondaryContainer
color md_theme_dark_onSurface
color md_theme_dark_onSurfaceVariant
color md_theme_dark_onTertiary
color md_theme_dark_onTertiaryContainer
color md_theme_dark_outline
color md_theme_dark_outlineVariant
color md_theme_dark_primary
color md_theme_dark_primaryContainer
color md_theme_dark_scrim
color md_theme_dark_secondary
color md_theme_dark_secondaryContainer
color md_theme_dark_surface
color md_theme_dark_surfaceVariant
color md_theme_dark_tertiary
color md_theme_dark_tertiaryContainer
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_scrim
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color seed
color white
drawable ic_launcher
drawable ic_launcher_background
drawable ic_launcher_foreground
mipmap ic_launcher
mipmap ic_launcher_round
string action_add_to_capstone
string action_go_to_resource
string action_mark_complete
string action_mark_in_progress
string app_name
string back
string cancel
string capstone_hub_title
string decisions_log_title
string delete
string edit
string node_detail_title
string prd_title
string project_tasks_title
string roadmap_subtitle
string roadmap_title
string save
string stage_architect_engineer
string stage_foundations
string stage_genai_specialist
string stage_ml_practitioner
string stage_product_visionary
string status_completed
string status_in_progress
string status_locked
string status_todo
style Base.Theme.CodexAIJourneyPlanner
style Theme.CodexAIJourneyPlanner
xml backup_rules
xml data_extraction_rules
