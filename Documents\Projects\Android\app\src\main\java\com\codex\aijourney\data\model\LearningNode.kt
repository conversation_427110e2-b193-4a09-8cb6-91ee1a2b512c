package com.codex.aijourney.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey

@Entity(
    tableName = "learning_nodes",
    foreignKeys = [
        ForeignKey(
            entity = LearningStage::class,
            parentColumns = ["id"],
            childColumns = ["stageId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class LearningNode(
    @PrimaryKey val id: String,
    val stageId: Int,
    val title: String,
    val description: String,
    val resourceUrl: String? = null,
    val order: Int,
    val status: NodeStatus = NodeStatus.LOCKED,
    val prerequisiteNodeIds: List<String> = emptyList(),
    val estimatedHours: Int = 0,
    val createdAt: Long = System.currentTimeMillis(),
    val completedAt: Long? = null
)

enum class NodeStatus {
    LOCKED,
    TODO,
    IN_PROGRESS,
    COMPLETED
}
