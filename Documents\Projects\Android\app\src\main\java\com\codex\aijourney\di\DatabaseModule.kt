package com.codex.aijourney.di

import android.content.Context
import androidx.room.Room
import com.codex.aijourney.data.database.CodexDatabase
import com.codex.aijourney.data.dao.LearningDao
import com.codex.aijourney.data.dao.CapstoneDao
import com.codex.aijourney.data.repository.LearningRepository
import com.codex.aijourney.data.repository.CapstoneRepository
import com.codex.aijourney.notifications.NotificationHelper
import com.codex.aijourney.notifications.NotificationScheduler
import com.codex.aijourney.data.preferences.NotificationPreferences
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideCodexDatabase(@ApplicationContext context: Context): CodexDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            CodexDatabase::class.java,
            "codex_database"
        )
        .fallbackToDestructiveMigration()
        .build()
    }
    
    @Provides
    fun provideLearningDao(database: CodexDatabase): LearningDao {
        return database.learningDao()
    }
    
    @Provides
    fun provideCapstoneDao(database: CodexDatabase): CapstoneDao {
        return database.capstoneDao()
    }

    @Provides
    @Singleton
    fun provideNotificationHelper(@ApplicationContext context: Context): NotificationHelper {
        return NotificationHelper(context)
    }

    @Provides
    @Singleton
    fun provideNotificationScheduler(@ApplicationContext context: Context): NotificationScheduler {
        return NotificationScheduler(context)
    }

    @Provides
    @Singleton
    fun provideNotificationPreferences(@ApplicationContext context: Context): NotificationPreferences {
        return NotificationPreferences(context)
    }
}
