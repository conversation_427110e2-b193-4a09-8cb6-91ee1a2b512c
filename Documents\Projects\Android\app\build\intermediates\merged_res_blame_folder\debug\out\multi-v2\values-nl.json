{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,403,517,599,693,805,953,1070,1208,1289,1384,1476,1575,1689,1806,1906,2033,2157,2289,2463,2590,2706,2824,2956,3049,3145,3261,3386,3482,3585,3684,3816,3952,4054,4153,4233,4312,4395,4478,4579,4655,4734,4829,4929,5020,5115,5199,5305,5402,5502,5618,5694,5791", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "168,281,398,512,594,688,800,948,1065,1203,1284,1379,1471,1570,1684,1801,1901,2028,2152,2284,2458,2585,2701,2819,2951,3044,3140,3256,3381,3477,3580,3679,3811,3947,4049,4148,4228,4307,4390,4473,4574,4650,4729,4824,4924,5015,5110,5194,5300,5397,5497,5613,5689,5786,5877"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,336,453,1476,1558,1652,1764,1912,2029,2167,2248,2343,2435,2534,2648,2765,2865,2992,3116,3248,3422,3549,3665,3783,3915,4008,4104,4220,4345,4441,4544,4643,4775,4911,5013,5308,5473,6194,6354,6538,6913,6989,7068,7163,7263,7354,7449,7533,7639,7736,7836,7952,8028,8125", "endColumns": "117,112,116,113,81,93,111,147,116,137,80,94,91,98,113,116,99,126,123,131,173,126,115,117,131,92,95,115,124,95,102,98,131,135,101,98,79,78,82,82,100,75,78,94,99,90,94,83,105,96,99,115,75,96,90", "endOffsets": "218,331,448,562,1553,1647,1759,1907,2024,2162,2243,2338,2430,2529,2643,2760,2860,2987,3111,3243,3417,3544,3660,3778,3910,4003,4099,4215,4340,4436,4539,4638,4770,4906,5008,5107,5383,5547,6272,6432,6634,6984,7063,7158,7258,7349,7444,7528,7634,7731,7831,7947,8023,8120,8211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "567,669,771,871,971,1078,1182,6437", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "664,766,866,966,1073,1177,1296,6533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1301,1393,5112,5209,5388,5552,5628,5724,5811,5900,5965,6030,6111,6277,6639,6723,6793", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "1388,1471,5204,5303,5468,5623,5719,5806,5895,5960,6025,6106,6189,6349,6718,6788,6908"}}]}]}