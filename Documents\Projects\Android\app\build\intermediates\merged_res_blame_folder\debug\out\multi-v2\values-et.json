{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "564,659,761,859,962,1068,1173,6372", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "654,756,854,957,1063,1168,1288,6468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1293,1386,5067,5163,5331,5491,5569,5660,5751,5835,5903,5969,6051,6219,6575,6652,6723", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "1381,1461,5158,5253,5408,5564,5655,5746,5830,5898,5964,6046,6131,6286,6647,6718,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,289,399,514,592,689,804,933,1049,1193,1276,1373,1463,1558,1670,1793,1893,2024,2153,2280,2451,2573,2688,2806,2925,3016,3109,3225,3355,3456,3555,3656,3782,3913,4017,4115,4188,4266,4349,4430,4532,4608,4690,4787,4887,4977,5077,5162,5267,5364,5466,5579,5655,5755", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "166,284,394,509,587,684,799,928,1044,1188,1271,1368,1458,1553,1665,1788,1888,2019,2148,2275,2446,2568,2683,2801,2920,3011,3104,3220,3350,3451,3550,3651,3777,3908,4012,4110,4183,4261,4344,4425,4527,4603,4685,4782,4882,4972,5072,5157,5262,5359,5461,5574,5650,5750,5845"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,339,449,1466,1544,1641,1756,1885,2001,2145,2228,2325,2415,2510,2622,2745,2845,2976,3105,3232,3403,3525,3640,3758,3877,3968,4061,4177,4307,4408,4507,4608,4734,4865,4969,5258,5413,6136,6291,6473,6845,6921,7003,7100,7200,7290,7390,7475,7580,7677,7779,7892,7968,8068", "endColumns": "115,117,109,114,77,96,114,128,115,143,82,96,89,94,111,122,99,130,128,126,170,121,114,117,118,90,92,115,129,100,98,100,125,130,103,97,72,77,82,80,101,75,81,96,99,89,99,84,104,96,101,112,75,99,94", "endOffsets": "216,334,444,559,1539,1636,1751,1880,1996,2140,2223,2320,2410,2505,2617,2740,2840,2971,3100,3227,3398,3520,3635,3753,3872,3963,4056,4172,4302,4403,4502,4603,4729,4860,4964,5062,5326,5486,6214,6367,6570,6916,6998,7095,7195,7285,7385,7470,7575,7672,7774,7887,7963,8063,8158"}}]}]}