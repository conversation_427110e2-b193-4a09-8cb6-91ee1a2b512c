-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:2:1-31:12
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:2:1-31:12
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a43c432c70263539fc03eaec385089b\transformed\hilt-work-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6002bc9d87cacde7c062153171ef94a4\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c1d31e4be7ec8711d14c13e923d397e\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\a998f09ae992dbb75cb85e58f811f968\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc7a5eb8beabf3de963d3879e4678baf\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd0d4f594363fb2fd35c820fca1e09f1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da7343ba5771349681ee327cfada024\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\f094ee834ecb5389cc80d9dfe1871d91\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\24c5ff9007f605f25bc54294b8f75119\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\00fd5f753aace3af8cd90dd3697b5d75\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7b2283e135e78ac38f9cae36e3843b4\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df5c27a64a460a07fd4d330cbe32d70d\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a994a422373fe9b1bb9136d9c7ba0bd5\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fa0e009891cf89811db30459237425f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3c4e1258f8502dcee2c331cc904dcbc\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef7aacd2bd1219a5a8fd224b57e8fc38\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8416124d36c6af6cc58f4a8786e9ded\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b698a0d0f65c40ee1a415cd6528006c0\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a05fc41fd3b15f1ad9f747ae0d0f3695\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c825e510e6da43e85e47478ae1492194\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\28c09791b00abb064460bccea86c8efd\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e8ef7d1c15beee35383ea45166550c4\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9ba94b5f0da7e0e31eef3f6a9285bfd\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e183b02138ac8db7cde14ce30222273e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c842739dc2efa7ed64266a5e05486da9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\584539fdf03ad19ce5c7f2b7fdd20583\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf36d25bc75d2fbef81de86830c66146\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e33d97a85b57ff84d207a6b17ccb869f\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21a60b3f72bf61694c5703b27aab89cb\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6f70bdbfa70946ba1ebe7a2087e9c8e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d03cf279d335049c401d4d568b74641b\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cd049769b1a759eb1dc6627bb73c648\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60731746ec9eff917ce1bb7cd65e3715\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4dcbe578dc6988eec89f554161f66f9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\941abf8432552465cecf048cb40e7e68\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8f8ff59751c742e95202a21a7db5869\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b41d762125eb57742305c4c67da3247\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf0e217971ffc20b6a95a8de2f30171b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e9a997749051d1d9106977ee6022b75\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c5ea7d236aa5acb59df179bdf867c4\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d690b48e52600432e47d5bd3e997cdae\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f41d4003b5686980b2f3c365d5bd63b0\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1111406b12509b34f86688129e1af7ed\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\52260c611c7a7096c50c0f8686a909fe\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d7c82c586998e852904de3ca590aa2\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a28daf378db10566c19a4527417b516\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36a284a0887b3c4c5321b113818582da\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\032342e3c6354cca2cc572f86017f7f1\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7500fea9d28fa54d06134522b034089e\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\305e62883e8abdbd2afbe8972f8b3ccb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b45d611169f374da18550ea7c1ca649\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c5859458394dae3f23afd0cbff23ae6\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\9022288a7d8f68b7e13e96d7f3beb803\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\74cdb92225f3c8986620bc107804edd3\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7116f78b39f0b4cdf70b6a7b1b02a676\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cacad7b34d8b903469477e2e2e4d1196\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\340517393d4d3210deb5535e48ded201\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9343521c7c2a7b874e855da4d2ab7126\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\351964def5e1d1c6dceaa658cce9af56\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:6:22-74
application
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:8:5-29:19
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:8:5-29:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\340517393d4d3210deb5535e48ded201\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\340517393d4d3210deb5535e48ded201\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:15:9-50
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:18:9-29
	android:icon
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:13:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:17:9-59
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:11:9-65
	android:name
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:9:9-41
activity#com.codex.aijourney.MainActivity
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:19:9-28:20
	android:label
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:22:13-45
	android:exported
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:21:13-36
	android:theme
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:23:13-63
	android:name
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:20:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:24:13-27:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:26:17-77
	android:name
		ADDED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:26:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a43c432c70263539fc03eaec385089b\transformed\hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a43c432c70263539fc03eaec385089b\transformed\hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6002bc9d87cacde7c062153171ef94a4\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6002bc9d87cacde7c062153171ef94a4\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c1d31e4be7ec8711d14c13e923d397e\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c1d31e4be7ec8711d14c13e923d397e\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\a998f09ae992dbb75cb85e58f811f968\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\a998f09ae992dbb75cb85e58f811f968\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc7a5eb8beabf3de963d3879e4678baf\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc7a5eb8beabf3de963d3879e4678baf\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd0d4f594363fb2fd35c820fca1e09f1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd0d4f594363fb2fd35c820fca1e09f1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da7343ba5771349681ee327cfada024\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2da7343ba5771349681ee327cfada024\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\f094ee834ecb5389cc80d9dfe1871d91\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\f094ee834ecb5389cc80d9dfe1871d91\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\24c5ff9007f605f25bc54294b8f75119\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\24c5ff9007f605f25bc54294b8f75119\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\00fd5f753aace3af8cd90dd3697b5d75\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\00fd5f753aace3af8cd90dd3697b5d75\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7b2283e135e78ac38f9cae36e3843b4\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7b2283e135e78ac38f9cae36e3843b4\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df5c27a64a460a07fd4d330cbe32d70d\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df5c27a64a460a07fd4d330cbe32d70d\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a994a422373fe9b1bb9136d9c7ba0bd5\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a994a422373fe9b1bb9136d9c7ba0bd5\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fa0e009891cf89811db30459237425f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fa0e009891cf89811db30459237425f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3c4e1258f8502dcee2c331cc904dcbc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3c4e1258f8502dcee2c331cc904dcbc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef7aacd2bd1219a5a8fd224b57e8fc38\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef7aacd2bd1219a5a8fd224b57e8fc38\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8416124d36c6af6cc58f4a8786e9ded\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c8416124d36c6af6cc58f4a8786e9ded\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b698a0d0f65c40ee1a415cd6528006c0\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b698a0d0f65c40ee1a415cd6528006c0\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a05fc41fd3b15f1ad9f747ae0d0f3695\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a05fc41fd3b15f1ad9f747ae0d0f3695\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c825e510e6da43e85e47478ae1492194\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c825e510e6da43e85e47478ae1492194\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\28c09791b00abb064460bccea86c8efd\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\28c09791b00abb064460bccea86c8efd\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e8ef7d1c15beee35383ea45166550c4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e8ef7d1c15beee35383ea45166550c4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9ba94b5f0da7e0e31eef3f6a9285bfd\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9ba94b5f0da7e0e31eef3f6a9285bfd\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e183b02138ac8db7cde14ce30222273e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e183b02138ac8db7cde14ce30222273e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c842739dc2efa7ed64266a5e05486da9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\c842739dc2efa7ed64266a5e05486da9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\584539fdf03ad19ce5c7f2b7fdd20583\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\584539fdf03ad19ce5c7f2b7fdd20583\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf36d25bc75d2fbef81de86830c66146\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf36d25bc75d2fbef81de86830c66146\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e33d97a85b57ff84d207a6b17ccb869f\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e33d97a85b57ff84d207a6b17ccb869f\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21a60b3f72bf61694c5703b27aab89cb\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21a60b3f72bf61694c5703b27aab89cb\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6f70bdbfa70946ba1ebe7a2087e9c8e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6f70bdbfa70946ba1ebe7a2087e9c8e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d03cf279d335049c401d4d568b74641b\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d03cf279d335049c401d4d568b74641b\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cd049769b1a759eb1dc6627bb73c648\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cd049769b1a759eb1dc6627bb73c648\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60731746ec9eff917ce1bb7cd65e3715\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60731746ec9eff917ce1bb7cd65e3715\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4dcbe578dc6988eec89f554161f66f9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4dcbe578dc6988eec89f554161f66f9\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\941abf8432552465cecf048cb40e7e68\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\941abf8432552465cecf048cb40e7e68\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8f8ff59751c742e95202a21a7db5869\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e8f8ff59751c742e95202a21a7db5869\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b41d762125eb57742305c4c67da3247\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b41d762125eb57742305c4c67da3247\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf0e217971ffc20b6a95a8de2f30171b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf0e217971ffc20b6a95a8de2f30171b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e9a997749051d1d9106977ee6022b75\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e9a997749051d1d9106977ee6022b75\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c5ea7d236aa5acb59df179bdf867c4\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c5ea7d236aa5acb59df179bdf867c4\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d690b48e52600432e47d5bd3e997cdae\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d690b48e52600432e47d5bd3e997cdae\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f41d4003b5686980b2f3c365d5bd63b0\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\f41d4003b5686980b2f3c365d5bd63b0\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1111406b12509b34f86688129e1af7ed\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1111406b12509b34f86688129e1af7ed\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\52260c611c7a7096c50c0f8686a909fe\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\52260c611c7a7096c50c0f8686a909fe\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d7c82c586998e852904de3ca590aa2\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d7c82c586998e852904de3ca590aa2\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a28daf378db10566c19a4527417b516\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a28daf378db10566c19a4527417b516\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36a284a0887b3c4c5321b113818582da\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36a284a0887b3c4c5321b113818582da\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\032342e3c6354cca2cc572f86017f7f1\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\032342e3c6354cca2cc572f86017f7f1\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7500fea9d28fa54d06134522b034089e\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7500fea9d28fa54d06134522b034089e\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\305e62883e8abdbd2afbe8972f8b3ccb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\305e62883e8abdbd2afbe8972f8b3ccb\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b45d611169f374da18550ea7c1ca649\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8b45d611169f374da18550ea7c1ca649\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c5859458394dae3f23afd0cbff23ae6\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c5859458394dae3f23afd0cbff23ae6\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\9022288a7d8f68b7e13e96d7f3beb803\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\9022288a7d8f68b7e13e96d7f3beb803\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\74cdb92225f3c8986620bc107804edd3\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\74cdb92225f3c8986620bc107804edd3\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7116f78b39f0b4cdf70b6a7b1b02a676\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7116f78b39f0b4cdf70b6a7b1b02a676\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cacad7b34d8b903469477e2e2e4d1196\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cacad7b34d8b903469477e2e2e4d1196\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\340517393d4d3210deb5535e48ded201\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\340517393d4d3210deb5535e48ded201\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9343521c7c2a7b874e855da4d2ab7126\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9343521c7c2a7b874e855da4d2ab7126\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\351964def5e1d1c6dceaa658cce9af56\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\351964def5e1d1c6dceaa658cce9af56\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b5c2a4308f293c798f5d1cd4bfb96df\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.codex.aijourney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.codex.aijourney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
