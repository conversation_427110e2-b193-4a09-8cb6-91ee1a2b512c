{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\941abf8432552465cecf048cb40e7e68\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "6145", "endColumns": "53", "endOffsets": "6194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eed21004deab6640172f1382b4314917\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,77,78,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,105,117,118,119,120,121,122,123,195,232,233,237,238,242,244,245,253,259,269,302,332,365", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,630,695,761,830,893,963,1031,1103,1173,1234,1308,1381,1442,1503,1565,1629,1691,1752,1820,1920,1980,2046,2119,2188,2245,2297,2359,2431,2507,4886,4921,5183,5238,5301,5356,5414,5472,5533,5596,5653,5704,5754,5815,5872,5938,5972,6007,6366,7206,7273,7345,7414,7483,7557,7629,12306,14253,14370,14571,14681,14882,15101,15173,15544,15747,16048,17779,18779,19461", "endLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,77,78,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,105,117,118,119,120,121,122,123,195,232,236,237,241,242,244,245,258,268,301,322,364,370", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,690,756,825,888,958,1026,1098,1168,1229,1303,1376,1437,1498,1560,1624,1686,1747,1815,1915,1975,2041,2114,2183,2240,2292,2354,2426,2502,2567,4916,4951,5233,5296,5351,5409,5467,5528,5591,5648,5699,5749,5810,5867,5933,5967,6002,6037,6431,7268,7340,7409,7478,7552,7624,7712,12372,14365,14566,14676,14877,15006,15168,15235,15742,16043,17774,18455,19456,19623"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,8", "startColumns": "4,4", "startOffsets": "139,375", "endLines": "6,8", "endColumns": "12,90", "endOffsets": "369,461"}, "to": {"startLines": "218,243", "startColumns": "4,4", "startOffsets": "13706,15011", "endLines": "221,243", "endColumns": "12,89", "endOffsets": "13830,15096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c2a4308f293c798f5d1cd4bfb96df\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "6700", "endColumns": "82", "endOffsets": "6778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2da7343ba5771349681ee327cfada024\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "79,246,399,402", "startColumns": "4,4,4,4", "startOffsets": "4956,15240,20891,21006", "endLines": "79,252,401,404", "endColumns": "52,24,24,24", "endOffsets": "5004,15539,21001,21116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e8f8ff59751c742e95202a21a7db5869\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6199", "endColumns": "49", "endOffsets": "6244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,104,126,127,165,166,169,172,173,175,177,178,179,182,183,188,201,202,203,222,225,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2572,2631,2690,2750,2810,2870,2930,2990,3050,3110,3170,3230,3290,3349,3409,3469,3529,3589,3649,3709,3769,3829,3889,3949,4008,4068,4128,4187,4246,4305,4364,4423,4482,4556,4614,4726,4777,6313,7819,7884,10584,10650,10837,10979,11031,11160,11281,11335,11371,11518,11568,11842,12654,12701,12737,13835,13947,14058", "endLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,104,126,127,165,166,169,172,173,175,177,178,179,182,183,188,201,202,203,224,227,231", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2626,2685,2745,2805,2865,2925,2985,3045,3105,3165,3225,3285,3344,3404,3464,3524,3584,3644,3704,3764,3824,3884,3944,4003,4063,4123,4182,4241,4300,4359,4418,4477,4551,4609,4664,4772,4827,6361,7879,7933,10645,10746,10890,11026,11086,11217,11330,11366,11400,11563,11617,11883,12696,12732,12822,13942,14053,14248"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "25,24,26,27,2,39,36,7,31,38,37,6,30,32,10,5,35,14,11,13,12,15,21,20,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1122,1057,1189,1252,55,1712,1590,264,1403,1670,1632,205,1352,1465,357,146,1552,636,427,564,494,718,976,917,822,871", "endColumns": "66,64,62,68,61,37,41,59,61,41,37,58,50,61,69,58,37,81,66,71,69,73,54,58,48,45", "endOffsets": "1184,1117,1247,1316,112,1745,1627,319,1460,1707,1665,259,1398,1522,422,200,1585,713,489,631,559,787,1026,971,866,912"}, "to": {"startLines": "106,107,108,109,111,112,124,125,164,167,170,176,180,181,184,185,186,190,191,192,193,194,196,197,198,199", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6436,6503,6568,6631,6783,6845,7717,7759,10522,10751,10895,11222,11405,11456,11622,11692,11751,11941,12023,12090,12162,12232,12377,12432,12491,12540", "endColumns": "66,64,62,68,61,37,41,59,61,41,37,58,50,61,69,58,37,81,66,71,69,73,54,58,48,45", "endOffsets": "6498,6563,6626,6695,6840,6878,7754,7814,10579,10788,10928,11276,11451,11513,11687,11746,11784,12018,12085,12157,12227,12301,12427,12486,12535,12581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9b41d762125eb57742305c4c67da3247\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "6042", "endColumns": "42", "endOffsets": "6080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36a284a0887b3c4c5321b113818582da\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "76,80", "startColumns": "4,4", "startOffsets": "4832,5009", "endColumns": "53,66", "endOffsets": "4881,5071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "113,114,115,116,128,129,130,131,132,133,136,137,138,139,140,141,142,143,144,145,146,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,168,171,174,187,189,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6883,6967,7049,7126,7938,7986,8047,8126,8228,8310,8426,8476,8541,8598,8663,8748,8839,8909,9002,9091,9185,9330,9417,9501,9593,9687,9747,9811,9894,9984,10047,10115,10183,10280,10385,10457,10793,10933,11091,11789,11888,12586,12827,12873,12923,12990,13057,13123,13188,13242,13314,13381,13451,13533,13579,13645", "endLines": "113,114,115,116,128,129,130,131,132,135,136,137,138,139,140,141,142,143,144,145,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,168,171,174,187,189,200,204,205,206,207,208,209,210,211,212,213,214,215,216,217", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "6962,7044,7121,7201,7981,8042,8121,8223,8305,8421,8471,8536,8593,8658,8743,8834,8904,8997,9086,9180,9325,9412,9496,9588,9682,9742,9806,9889,9979,10042,10110,10178,10275,10380,10452,10517,10832,10974,11155,11837,11936,12649,12868,12918,12985,13052,13118,13183,13237,13309,13376,13446,13528,13574,13640,13701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cc7a5eb8beabf3de963d3879e4678baf\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "371,384,390,396,405", "startColumns": "4,4,4,4,4", "startOffsets": "19628,20267,20511,20758,21121", "endLines": "383,389,395,398,409", "endColumns": "24,24,24,24,24", "endOffsets": "20262,20506,20753,20886,21298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c7b2283e135e78ac38f9cae36e3843b4\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "73,82,103,323,328", "startColumns": "4,4,4,4,4", "startOffsets": "4669,5118,6249,18460,18630", "endLines": "73,82,103,327,331", "endColumns": "56,64,63,24,24", "endOffsets": "4721,5178,6308,18625,18774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1111406b12509b34f86688129e1af7ed\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "81,100", "startColumns": "4,4", "startOffsets": "5076,6085", "endColumns": "41,59", "endOffsets": "5113,6140"}}]}]}