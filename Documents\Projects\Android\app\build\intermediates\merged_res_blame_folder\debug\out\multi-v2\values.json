{"logs": [{"outputFile": "com.codex.aijourney.app-mergeDebugResources-61:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\941abf8432552465cecf048cb40e7e68\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "154", "startColumns": "4", "startOffsets": "9339", "endColumns": "53", "endOffsets": "9388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eed21004deab6640172f1382b4314917\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,10,11,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,130,131,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,158,170,171,172,173,174,175,176,248,300,301,305,306,310,312,313,321,327,337,370,400,433", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,672,737,3874,3943,4087,4157,4225,4297,4367,4428,4502,4575,4636,4697,4759,4823,4885,4946,5014,5114,5174,5240,5313,5382,5439,5491,5553,5625,5701,8080,8115,8377,8432,8495,8550,8608,8666,8727,8790,8847,8898,8948,9009,9066,9132,9166,9201,9560,10400,10467,10539,10608,10677,10751,10823,15500,18430,18547,18748,18858,19059,19278,19350,19721,19924,20225,21956,22956,23638", "endLines": "2,7,8,10,11,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,130,131,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,158,170,171,172,173,174,175,176,248,300,304,305,309,310,312,313,326,336,369,390,432,438", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,732,798,3938,4001,4152,4220,4292,4362,4423,4497,4570,4631,4692,4754,4818,4880,4941,5009,5109,5169,5235,5308,5377,5434,5486,5548,5620,5696,5761,8110,8145,8427,8490,8545,8603,8661,8722,8785,8842,8893,8943,9004,9061,9127,9161,9196,9231,9625,10462,10534,10603,10672,10746,10818,10906,15566,18542,18743,18853,19054,19183,19345,19412,19919,20220,21951,22632,23633,23800"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,23", "startColumns": "4,4", "startOffsets": "139,1326", "endLines": "21,23", "endColumns": "12,90", "endOffsets": "1320,1412"}, "to": {"startLines": "271,311", "startColumns": "4,4", "startOffsets": "16900,19188", "endLines": "289,311", "endColumns": "12,89", "endOffsets": "18007,19273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2b5c2a4308f293c798f5d1cd4bfb96df\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "163", "startColumns": "4", "startOffsets": "9894", "endColumns": "82", "endOffsets": "9972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2da7343ba5771349681ee327cfada024\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "132,314,467,470", "startColumns": "4,4,4,4", "startOffsets": "8150,19417,25068,25183", "endLines": "132,320,469,472", "endColumns": "52,24,24,24", "endOffsets": "8198,19716,25178,25293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e8f8ff59751c742e95202a21a7db5869\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "9393", "endColumns": "49", "endOffsets": "9438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,127,128,157,179,180,218,219,222,225,226,228,230,231,232,235,236,241,254,255,256,290,293,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5766,5825,5884,5944,6004,6064,6124,6184,6244,6304,6364,6424,6484,6543,6603,6663,6723,6783,6843,6903,6963,7023,7083,7143,7202,7262,7322,7381,7440,7499,7558,7617,7676,7750,7808,7920,7971,9507,11013,11078,13778,13844,14031,14173,14225,14354,14475,14529,14565,14712,14762,15036,15848,15895,15931,18012,18124,18235", "endLines": "91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,127,128,157,179,180,218,219,222,225,226,228,230,231,232,235,236,241,254,255,256,292,295,299", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "5820,5879,5939,5999,6059,6119,6179,6239,6299,6359,6419,6479,6538,6598,6658,6718,6778,6838,6898,6958,7018,7078,7138,7197,7257,7317,7376,7435,7494,7553,7612,7671,7745,7803,7858,7966,8021,9555,11073,11127,13839,13940,14084,14220,14280,14411,14524,14560,14594,14757,14811,15077,15890,15926,16016,18119,18230,18425"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,52,48,50,53,49,51,37,39,41,43,55,57,45,47,58,59,36,38,60,40,42,54,56,44,46,25,21,23,26,22,24,10,12,14,16,28,30,18,20,31,32,9,11,33,13,15,27,29,17,19,6,3", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,2827,2589,2699,2886,2643,2762,1895,2018,2143,2270,3003,3124,2396,2521,3189,3245,1839,1953,3308,2085,2203,2947,3061,2339,2455,1259,1017,1129,1319,1072,1193,312,437,564,693,1438,1561,821,948,1627,1684,255,371,1748,505,625,1381,1497,763,881,179,97", "endColumns": "41,58,53,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,53,57,66,55,62,56,65,59,54,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,54,58,67,56,63,57,66,38,41", "endOffsets": "92,2881,2638,2757,2942,2694,2822,1948,2080,2198,2334,3056,3184,2450,2584,3240,3303,1890,2013,3357,2138,2265,2998,3119,2391,2516,1314,1067,1188,1376,1124,1254,366,500,620,758,1492,1622,876,1012,1679,1743,307,432,1798,559,688,1433,1556,816,943,213,134"}, "to": {"startLines": "9,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "630,803,862,916,979,1040,1096,1161,1219,1286,1346,1415,1473,1538,1597,1665,1721,1784,1840,1905,1959,2017,2084,2140,2203,2260,2326,2386,2441,2505,2567,2624,2690,2749,2817,2878,2948,3007,3073,3133,3202,3259,3323,3380,3446,3501,3560,3628,3685,3749,3807,4006,4045", "endColumns": "41,58,53,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,53,57,66,55,62,56,65,59,54,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,54,58,67,56,63,57,66,38,41", "endOffsets": "667,857,911,974,1035,1091,1156,1214,1281,1341,1410,1468,1533,1592,1660,1716,1779,1835,1900,1954,2012,2079,2135,2198,2255,2321,2381,2436,2500,2562,2619,2685,2744,2812,2873,2943,3002,3068,3128,3197,3254,3318,3375,3441,3496,3555,3623,3680,3744,3802,3869,4040,4082"}}, {"source": "C:\\Users\\<USER>\\Documents\\Projects\\Android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "25,24,26,27,2,39,36,7,31,38,37,6,30,32,10,5,35,14,11,13,12,15,21,20,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1122,1057,1189,1252,55,1712,1590,264,1403,1670,1632,205,1352,1465,357,146,1552,636,427,564,494,718,976,917,822,871", "endColumns": "66,64,62,68,61,37,41,59,61,41,37,58,50,61,69,58,37,81,66,71,69,73,54,58,48,45", "endOffsets": "1184,1117,1247,1316,112,1745,1627,319,1460,1707,1665,259,1398,1522,422,200,1585,713,489,631,559,787,1026,971,866,912"}, "to": {"startLines": "159,160,161,162,164,165,177,178,217,220,223,229,233,234,237,238,239,243,244,245,246,247,249,250,251,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9630,9697,9762,9825,9977,10039,10911,10953,13716,13945,14089,14416,14599,14650,14816,14886,14945,15135,15217,15284,15356,15426,15571,15626,15685,15734", "endColumns": "66,64,62,68,61,37,41,59,61,41,37,58,50,61,69,58,37,81,66,71,69,73,54,58,48,45", "endOffsets": "9692,9757,9820,9889,10034,10072,10948,11008,13773,13982,14122,14470,14645,14707,14881,14940,14978,15212,15279,15351,15421,15495,15621,15680,15729,15775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9b41d762125eb57742305c4c67da3247\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "9236", "endColumns": "42", "endOffsets": "9274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\36a284a0887b3c4c5321b113818582da\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "129,133", "startColumns": "4,4", "startOffsets": "8026,8203", "endColumns": "53,66", "endOffsets": "8075,8265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00fd5f753aace3af8cd90dd3697b5d75\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "166,167,168,169,181,182,183,184,185,186,189,190,191,192,193,194,195,196,197,198,199,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,221,224,227,240,242,253,257,258,259,260,261,262,263,264,265,266,267,268,269,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10077,10161,10243,10320,11132,11180,11241,11320,11422,11504,11620,11670,11735,11792,11857,11942,12033,12103,12196,12285,12379,12524,12611,12695,12787,12881,12941,13005,13088,13178,13241,13309,13377,13474,13579,13651,13987,14127,14285,14983,15082,15780,16021,16067,16117,16184,16251,16317,16382,16436,16508,16575,16645,16727,16773,16839", "endLines": "166,167,168,169,181,182,183,184,185,188,189,190,191,192,193,194,195,196,197,198,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,221,224,227,240,242,253,257,258,259,260,261,262,263,264,265,266,267,268,269,270", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "10156,10238,10315,10395,11175,11236,11315,11417,11499,11615,11665,11730,11787,11852,11937,12028,12098,12191,12280,12374,12519,12606,12690,12782,12876,12936,13000,13083,13173,13236,13304,13372,13469,13574,13646,13711,14026,14168,14349,15031,15130,15843,16062,16112,16179,16246,16312,16377,16431,16503,16570,16640,16722,16768,16834,16895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cc7a5eb8beabf3de963d3879e4678baf\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "439,452,458,464,473", "startColumns": "4,4,4,4,4", "startOffsets": "23805,24444,24688,24935,25298", "endLines": "451,457,463,466,477", "endColumns": "24,24,24,24,24", "endOffsets": "24439,24683,24930,25063,25475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c7b2283e135e78ac38f9cae36e3843b4\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "126,135,156,391,396", "startColumns": "4,4,4,4,4", "startOffsets": "7863,8312,9443,22637,22807", "endLines": "126,135,156,395,399", "endColumns": "56,64,63,24,24", "endOffsets": "7915,8372,9502,22802,22951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1111406b12509b34f86688129e1af7ed\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "134,153", "startColumns": "4,4", "startOffsets": "8270,9279", "endColumns": "41,59", "endOffsets": "8307,9334"}}]}]}