{"logs": [{"outputFile": "com.codex.aijourney.test.app-mergeDebugAndroidTestResources-32:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\26c5ea7d236aa5acb59df179bdf867c4\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,934,1023,1119,1222,1312,1398,1486,1579,1663,1733,1803,1888,1975,2149,2227,2295", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "929,1018,1114,1217,1307,1393,1481,1574,1658,1728,1798,1883,1970,2043,2222,2290,2412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\003b9c8fc724b7946ce98db2266de440\\transformed\\core-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,613,721,2048", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "198,300,400,501,608,716,831,2144"}}]}]}