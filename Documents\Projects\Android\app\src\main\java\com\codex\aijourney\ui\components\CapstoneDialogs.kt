package com.codex.aijourney.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.codex.aijourney.data.model.CapstoneProject
import com.codex.aijourney.data.model.TaskPriority

@Composable
fun PRDEditDialog(
    project: CapstoneProject?,
    onSave: (CapstoneProject) -> Unit,
    onCancel: () -> Unit
) {
    var title by remember { mutableStateOf(project?.title ?: "") }
    var problem by remember { mutableStateOf(project?.problem ?: "") }
    var targetUser by remember { mutableStateOf(project?.targetUser ?: "") }
    var mvpFeatures by remember { mutableStateOf(project?.mvpFeatures ?: "") }
    
    Dialog(onDismissRequest = onCancel) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f)
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(20.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                Text(
                    text = "📋 Edit Project PRD",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // Project Title
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("Project Title") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("e.g., AI-Powered Recipe Recommender") }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Problem Statement
                OutlinedTextField(
                    value = problem,
                    onValueChange = { problem = it },
                    label = { Text("Problem Statement") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    maxLines = 4,
                    placeholder = { Text("What problem are you solving? Who has this problem and why is it important?") }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Target User
                OutlinedTextField(
                    value = targetUser,
                    onValueChange = { targetUser = it },
                    label = { Text("Target User") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    maxLines = 4,
                    placeholder = { Text("Who are your users? What are their characteristics, needs, and pain points?") }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // MVP Features
                OutlinedTextField(
                    value = mvpFeatures,
                    onValueChange = { mvpFeatures = it },
                    label = { Text("MVP Features") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 5,
                    placeholder = { Text("List the core features needed for your minimum viable product...") }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onCancel) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            val updatedProject = (project ?: CapstoneProject()).copy(
                                title = title.trim(),
                                problem = problem.trim(),
                                targetUser = targetUser.trim(),
                                mvpFeatures = mvpFeatures.trim(),
                                updatedAt = System.currentTimeMillis()
                            )
                            onSave(updatedProject)
                        },
                        enabled = title.trim().isNotEmpty()
                    ) {
                        Text("Save PRD")
                    }
                }
            }
        }
    }
}

@Composable
fun TaskEditDialog(
    title: String,
    description: String,
    priority: TaskPriority,
    isEditing: Boolean,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onPriorityChange: (TaskPriority) -> Unit,
    onSave: () -> Unit,
    onCancel: () -> Unit
) {
    Dialog(onDismissRequest = onCancel) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = if (isEditing) "✏️ Edit Task" else "➕ Add Task",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // Task Title
                OutlinedTextField(
                    value = title,
                    onValueChange = onTitleChange,
                    label = { Text("Task Title") },
                    modifier = Modifier.fillMaxWidth(),
                    placeholder = { Text("What needs to be done?") }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Task Description
                OutlinedTextField(
                    value = description,
                    onValueChange = onDescriptionChange,
                    label = { Text("Description (Optional)") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    maxLines = 4,
                    placeholder = { Text("Add more details about this task...") }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Priority Selection
                Text(
                    text = "Priority",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TaskPriority.values().forEach { taskPriority ->
                        FilterChip(
                            onClick = { onPriorityChange(taskPriority) },
                            label = { Text(taskPriority.name) },
                            selected = priority == taskPriority,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onCancel) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = onSave,
                        enabled = title.trim().isNotEmpty()
                    ) {
                        Text(if (isEditing) "Update" else "Add Task")
                    }
                }
            }
        }
    }
}

@Composable
fun DecisionEditDialog(
    decision: String,
    reasoning: String,
    isEditing: Boolean,
    onDecisionChange: (String) -> Unit,
    onReasoningChange: (String) -> Unit,
    onSave: () -> Unit,
    onCancel: () -> Unit
) {
    Dialog(onDismissRequest = onCancel) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = if (isEditing) "🧠 Edit Decision" else "🧠 Add Decision",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // Decision
                OutlinedTextField(
                    value = decision,
                    onValueChange = onDecisionChange,
                    label = { Text("Decision") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    maxLines = 4,
                    placeholder = { Text("What decision did you make?") }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Reasoning
                OutlinedTextField(
                    value = reasoning,
                    onValueChange = onReasoningChange,
                    label = { Text("Reasoning (Optional)") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 5,
                    placeholder = { Text("Why did you make this decision? What factors influenced it?") }
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onCancel) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = onSave,
                        enabled = decision.trim().isNotEmpty()
                    ) {
                        Text(if (isEditing) "Update" else "Add Decision")
                    }
                }
            }
        }
    }
}
