package com.codex.aijourney.workers

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.codex.aijourney.data.model.NodeStatus
import com.codex.aijourney.data.repository.LearningRepository
import com.codex.aijourney.notifications.NotificationHelper
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.first

@HiltWorker
class LearningReminderWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val learningRepository: LearningRepository,
    private val notificationHelper: NotificationHelper
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            val allNodes = learningRepository.getAllNodes().first()
            val stages = learningRepository.getAllStages().first()
            
            // Find current stage and in-progress nodes
            val inProgressNodes = allNodes.filter { it.status == NodeStatus.IN_PROGRESS }
            val todoNodes = allNodes.filter { it.status == NodeStatus.TODO }
            val completedNodes = allNodes.filter { it.status == NodeStatus.COMPLETED }
            
            // Determine current stage
            val currentStage = stages.find { stage ->
                val stageNodes = allNodes.filter { it.stageId == stage.id }
                stageNodes.any { it.status == NodeStatus.IN_PROGRESS || it.status == NodeStatus.TODO }
            }
            
            when {
                // User has in-progress nodes - remind to continue
                inProgressNodes.isNotEmpty() -> {
                    val node = inProgressNodes.first()
                    val stage = stages.find { it.id == node.stageId }
                    
                    notificationHelper.showContextualNudge(
                        stage = stage?.title ?: "Learning",
                        message = "You're making great progress on '${node.title}'! Ready to continue your learning journey?",
                        actionText = "Continue Learning"
                    )
                }
                
                // User has available nodes but none in progress
                todoNodes.isNotEmpty() -> {
                    val node = todoNodes.first()
                    val stage = stages.find { it.id == node.stageId }
                    
                    notificationHelper.showContextualNudge(
                        stage = stage?.title ?: "Learning",
                        message = "Ready to start '${node.title}'? This module will take about ${node.estimatedHours} hours to complete.",
                        actionText = "Start Learning"
                    )
                }
                
                // User completed recent nodes - encourage next steps
                completedNodes.isNotEmpty() -> {
                    val recentlyCompleted = completedNodes
                        .filter { it.completedAt != null }
                        .sortedByDescending { it.completedAt }
                        .take(1)
                    
                    if (recentlyCompleted.isNotEmpty()) {
                        val node = recentlyCompleted.first()
                        val stage = stages.find { it.id == node.stageId }
                        
                        notificationHelper.showContextualNudge(
                            stage = stage?.title ?: "Learning",
                            message = "Great job completing '${node.title}'! What insights did you gain? Consider adding them to your capstone project.",
                            actionText = "Add Insights"
                        )
                    }
                }
                
                else -> {
                    // Fallback general encouragement
                    notificationHelper.showLearningReminder(
                        title = "🚀 Keep Learning!",
                        message = "Your AI journey awaits! Check out your learning roadmap to see what's next."
                    )
                }
            }
            
            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }
    
    companion object {
        const val WORK_NAME = "learning_reminder_work"
    }
}
