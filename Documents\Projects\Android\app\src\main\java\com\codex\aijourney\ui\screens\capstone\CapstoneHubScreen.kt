package com.codex.aijourney.ui.screens.capstone

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.codex.aijourney.ui.components.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CapstoneHubScreen(
    onBackClick: () -> Unit,
    viewModel: CapstoneHubViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    // Handle errors
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // In a real app, show snackbar
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("🎯 Capstone Hub") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    // Quick add buttons
                    IconButton(onClick = { viewModel.startAddingTask() }) {
                        Icon(Icons.Default.Add, contentDescription = "Add Task")
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { viewModel.startAddingDecision() }
            ) {
                Icon(Icons.Default.Psychology, contentDescription = "Add Decision")
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator()
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Loading your project...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.Gray
                            )
                        }
                    }
                }

                uiState.error != null -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "⚠️",
                                style = MaterialTheme.typography.headlineLarge
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Failed to load project",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = uiState.error ?: "Unknown error",
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                color = Color.Gray
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(
                                onClick = { viewModel.clearError() }
                            ) {
                                Text("Try Again")
                            }
                        }
                    }
                }

                else -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // Project Overview
                        item {
                            ProjectOverviewCard(
                                project = uiState.project,
                                progress = uiState.progress,
                                onEditClick = { viewModel.startEditingPRD() }
                            )
                        }

                        // Tasks Section
                        item {
                            SectionHeader(
                                title = "📋 Project Tasks",
                                subtitle = "${uiState.progress.tasksCompleted}/${uiState.progress.totalTasks} completed",
                                onAddClick = { viewModel.startAddingTask() }
                            )
                        }

                        if (uiState.tasks.isEmpty()) {
                            item {
                                EmptyStateCard(
                                    icon = "📋",
                                    title = "No tasks yet",
                                    description = "Add your first project task to get started",
                                    actionText = "Add Task",
                                    onActionClick = { viewModel.startAddingTask() }
                                )
                            }
                        } else {
                            items(uiState.tasks) { task ->
                                TaskCard(
                                    task = task,
                                    onToggleComplete = { viewModel.toggleTaskCompletion(task.id) },
                                    onEdit = { viewModel.startEditingTask(task) },
                                    onDelete = { viewModel.deleteTask(task) }
                                )
                            }
                        }

                        // Decision Logs Section
                        item {
                            SectionHeader(
                                title = "🧠 Decision Log",
                                subtitle = "${uiState.decisionLogs.size} decisions recorded",
                                onAddClick = { viewModel.startAddingDecision() }
                            )
                        }

                        if (uiState.decisionLogs.isEmpty()) {
                            item {
                                EmptyStateCard(
                                    icon = "🧠",
                                    title = "No decisions logged",
                                    description = "Track your technical and product decisions",
                                    actionText = "Add Decision",
                                    onActionClick = { viewModel.startAddingDecision() }
                                )
                            }
                        } else {
                            items(uiState.decisionLogs) { decision ->
                                DecisionLogCard(
                                    decision = decision,
                                    onEdit = { viewModel.startEditingDecision(decision) },
                                    onDelete = { viewModel.deleteDecision(decision) }
                                )
                            }
                        }

                        // Bottom spacing for FAB
                        item {
                            Spacer(modifier = Modifier.height(80.dp))
                        }
                    }
                }
            }
        }
    }

    // PRD Edit Dialog
    if (uiState.isEditingPRD) {
        PRDEditDialog(
            project = uiState.project,
            onSave = { project -> viewModel.updateProject(project) },
            onCancel = { viewModel.cancelEditingPRD() }
        )
    }

    // Task Edit Dialog
    if (uiState.isAddingTask) {
        TaskEditDialog(
            title = uiState.newTaskTitle,
            description = uiState.newTaskDescription,
            priority = uiState.newTaskPriority,
            isEditing = uiState.editingTaskId != null,
            onTitleChange = viewModel::updateTaskTitle,
            onDescriptionChange = viewModel::updateTaskDescription,
            onPriorityChange = viewModel::updateTaskPriority,
            onSave = viewModel::saveTask,
            onCancel = viewModel::cancelAddingTask
        )
    }

    // Decision Edit Dialog
    if (uiState.isAddingDecision) {
        DecisionEditDialog(
            decision = uiState.newDecisionText,
            reasoning = uiState.newDecisionReasoning,
            isEditing = uiState.editingDecisionId != null,
            onDecisionChange = viewModel::updateDecisionText,
            onReasoningChange = viewModel::updateDecisionReasoning,
            onSave = viewModel::saveDecision,
            onCancel = viewModel::cancelAddingDecision
        )
    }
}

@Composable
private fun SectionHeader(
    title: String,
    subtitle: String,
    onAddClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }

        IconButton(onClick = onAddClick) {
            Icon(
                Icons.Default.Add,
                contentDescription = "Add",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
private fun EmptyStateCard(
    icon: String,
    title: String,
    description: String,
    actionText: String,
    onActionClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F9FA)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = icon,
                style = MaterialTheme.typography.headlineLarge
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onActionClick) {
                Text(actionText)
            }
        }
    }
}
