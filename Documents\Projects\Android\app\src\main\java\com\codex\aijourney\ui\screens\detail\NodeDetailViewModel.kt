package com.codex.aijourney.ui.screens.detail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.codex.aijourney.data.model.LearningNode
import com.codex.aijourney.data.model.NodeStatus
import com.codex.aijourney.data.model.UserNote
import com.codex.aijourney.data.repository.LearningRepository
import com.codex.aijourney.data.repository.CapstoneRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

data class NodeDetailUiState(
    val node: LearningNode? = null,
    val notes: List<UserNote> = emptyList(),
    val isLoading: Boolean = true,
    val error: String? = null,
    val isEditingNote: Boolean = false,
    val editingNoteId: String? = null,
    val noteContent: String = ""
)

@HiltViewModel
class NodeDetailViewModel @Inject constructor(
    private val learningRepository: LearningRepository,
    private val capstoneRepository: CapstoneRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(NodeDetailUiState())
    val uiState: StateFlow<NodeDetailUiState> = _uiState.asStateFlow()
    
    private var currentNodeId: String = ""
    
    fun loadNodeDetails(nodeId: String) {
        currentNodeId = nodeId
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                combine(
                    flow { emit(learningRepository.getNodeById(nodeId)) },
                    learningRepository.getNotesByNode(nodeId)
                ) { node, notes ->
                    NodeDetailUiState(
                        node = node,
                        notes = notes,
                        isLoading = false
                    )
                }.catch { exception ->
                    _uiState.value = NodeDetailUiState(
                        error = "Failed to load node details: ${exception.message}",
                        isLoading = false
                    )
                }.collect { newState ->
                    _uiState.value = newState
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load node: ${e.message}",
                    isLoading = false
                )
            }
        }
    }
    
    fun updateNodeStatus(newStatus: NodeStatus) {
        viewModelScope.launch {
            try {
                learningRepository.updateNodeStatus(currentNodeId, newStatus)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update status: ${e.message}"
                )
            }
        }
    }
    
    fun startEditingNote(noteId: String? = null) {
        val existingNote = if (noteId != null) {
            _uiState.value.notes.find { it.id == noteId }
        } else null
        
        _uiState.value = _uiState.value.copy(
            isEditingNote = true,
            editingNoteId = noteId,
            noteContent = existingNote?.content ?: ""
        )
    }
    
    fun updateNoteContent(content: String) {
        _uiState.value = _uiState.value.copy(noteContent = content)
    }
    
    fun saveNote() {
        viewModelScope.launch {
            try {
                val currentState = _uiState.value
                val noteContent = currentState.noteContent.trim()
                
                if (noteContent.isEmpty()) {
                    cancelEditingNote()
                    return@launch
                }
                
                if (currentState.editingNoteId != null) {
                    // Update existing note
                    val existingNote = currentState.notes.find { it.id == currentState.editingNoteId }
                    existingNote?.let { note ->
                        val updatedNote = note.copy(
                            content = noteContent,
                            updatedAt = System.currentTimeMillis()
                        )
                        learningRepository.updateNote(updatedNote)
                    }
                } else {
                    // Create new note
                    val newNote = UserNote(
                        id = UUID.randomUUID().toString(),
                        nodeId = currentNodeId,
                        content = noteContent,
                        createdAt = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis()
                    )
                    learningRepository.insertNote(newNote)
                }
                
                cancelEditingNote()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to save note: ${e.message}"
                )
            }
        }
    }
    
    fun deleteNote(noteId: String) {
        viewModelScope.launch {
            try {
                val note = _uiState.value.notes.find { it.id == noteId }
                note?.let {
                    learningRepository.deleteNote(it)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete note: ${e.message}"
                )
            }
        }
    }
    
    fun cancelEditingNote() {
        _uiState.value = _uiState.value.copy(
            isEditingNote = false,
            editingNoteId = null,
            noteContent = ""
        )
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun getFormattedDuration(): String {
        val node = _uiState.value.node ?: return ""
        val hours = node.estimatedHours
        return when {
            hours == 0 -> "Duration not specified"
            hours < 10 -> "$hours hours"
            hours < 40 -> "${hours / 8} days (${hours}h)"
            else -> "${hours / 40} weeks (${hours}h)"
        }
    }
    
    fun getStatusDisplayText(): String {
        return when (_uiState.value.node?.status) {
            NodeStatus.LOCKED -> "🔒 Locked - Complete prerequisites first"
            NodeStatus.TODO -> "📚 Ready to start"
            NodeStatus.IN_PROGRESS -> "⚡ Currently learning"
            NodeStatus.COMPLETED -> "✅ Completed"
            null -> ""
        }
    }
    
    fun canChangeStatus(): Boolean {
        return _uiState.value.node?.status != NodeStatus.LOCKED
    }
    
    fun getNextStatusAction(): Pair<NodeStatus, String>? {
        return when (_uiState.value.node?.status) {
            NodeStatus.TODO -> NodeStatus.IN_PROGRESS to "Start Learning"
            NodeStatus.IN_PROGRESS -> NodeStatus.COMPLETED to "Mark Complete"
            NodeStatus.COMPLETED -> NodeStatus.IN_PROGRESS to "Mark In Progress"
            else -> null
        }
    }

    fun addToCapstoneProject() {
        viewModelScope.launch {
            try {
                val node = _uiState.value.node ?: return@launch
                capstoneRepository.addNodeToProjectTasks(node.title, node.description)

                // Show success message (in a real app, you'd use a snackbar)
                _uiState.value = _uiState.value.copy(
                    error = "Added to Capstone Project successfully!"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to add to capstone: ${e.message}"
                )
            }
        }
    }

    fun addLearningInsight(insight: String) {
        viewModelScope.launch {
            try {
                val node = _uiState.value.node ?: return@launch
                capstoneRepository.addLearningInsightToCapstone(node.title, insight)

                // Show success message
                _uiState.value = _uiState.value.copy(
                    error = "Learning insight added to capstone!"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to add insight: ${e.message}"
                )
            }
        }
    }
}
