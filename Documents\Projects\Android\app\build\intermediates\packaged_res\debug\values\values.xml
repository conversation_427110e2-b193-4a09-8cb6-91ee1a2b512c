<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="md_theme_dark_background">#1c1b1f</color>
    <color name="md_theme_dark_error">#ffb4ab</color>
    <color name="md_theme_dark_errorContainer">#93000a</color>
    <color name="md_theme_dark_onBackground">#e6e1e5</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#ffdad6</color>
    <color name="md_theme_dark_onPrimary">#381e72</color>
    <color name="md_theme_dark_onPrimaryContainer">#eaddff</color>
    <color name="md_theme_dark_onSecondary">#332d41</color>
    <color name="md_theme_dark_onSecondaryContainer">#e8def8</color>
    <color name="md_theme_dark_onSurface">#e6e1e5</color>
    <color name="md_theme_dark_onSurfaceVariant">#cab6d0</color>
    <color name="md_theme_dark_onTertiary">#492532</color>
    <color name="md_theme_dark_onTertiaryContainer">#ffd8e4</color>
    <color name="md_theme_dark_outline">#938f99</color>
    <color name="md_theme_dark_outlineVariant">#49454f</color>
    <color name="md_theme_dark_primary">#d0bcff</color>
    <color name="md_theme_dark_primaryContainer">#4f378b</color>
    <color name="md_theme_dark_scrim">#000000</color>
    <color name="md_theme_dark_secondary">#ccc2dc</color>
    <color name="md_theme_dark_secondaryContainer">#4a4458</color>
    <color name="md_theme_dark_surface">#1c1b1f</color>
    <color name="md_theme_dark_surfaceVariant">#49454f</color>
    <color name="md_theme_dark_tertiary">#efb8c8</color>
    <color name="md_theme_dark_tertiaryContainer">#633b48</color>
    <color name="md_theme_light_background">#fffbfe</color>
    <color name="md_theme_light_error">#ba1a1a</color>
    <color name="md_theme_light_errorContainer">#ffdad6</color>
    <color name="md_theme_light_onBackground">#1c1b1f</color>
    <color name="md_theme_light_onError">#ffffff</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#ffffff</color>
    <color name="md_theme_light_onPrimaryContainer">#21005d</color>
    <color name="md_theme_light_onSecondary">#ffffff</color>
    <color name="md_theme_light_onSecondaryContainer">#1d192b</color>
    <color name="md_theme_light_onSurface">#1c1b1f</color>
    <color name="md_theme_light_onSurfaceVariant">#49454f</color>
    <color name="md_theme_light_onTertiary">#ffffff</color>
    <color name="md_theme_light_onTertiaryContainer">#31111d</color>
    <color name="md_theme_light_outline">#79747e</color>
    <color name="md_theme_light_outlineVariant">#cab6d0</color>
    <color name="md_theme_light_primary">#6650a4</color>
    <color name="md_theme_light_primaryContainer">#eaddff</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_secondary">#625b71</color>
    <color name="md_theme_light_secondaryContainer">#e8def8</color>
    <color name="md_theme_light_surface">#fffbfe</color>
    <color name="md_theme_light_surfaceVariant">#e7e0ec</color>
    <color name="md_theme_light_tertiary">#7d5260</color>
    <color name="md_theme_light_tertiaryContainer">#ffd8e4</color>
    <color name="seed">#6650a4</color>
    <color name="white">#FFFFFFFF</color>
    <string name="action_add_to_capstone">Add to Capstone</string>
    <string name="action_go_to_resource">Go to Resource</string>
    <string name="action_mark_complete">Mark Complete</string>
    <string name="action_mark_in_progress">Mark In Progress</string>
    <string name="app_name">Codex AI Journey Planner</string>
    <string name="back">Back</string>
    <string name="cancel">Cancel</string>
    <string name="capstone_hub_title">Capstone Hub</string>
    <string name="decisions_log_title">Decisions Log</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="node_detail_title">Node Details</string>
    <string name="prd_title">One-Page PRD</string>
    <string name="project_tasks_title">Project Tasks</string>
    <string name="roadmap_subtitle">Your AI Learning Journey</string>
    <string name="roadmap_title">Learning Roadmap</string>
    <string name="save">Save</string>
    <string name="stage_architect_engineer">The Architect &amp; Engineer</string>
    <string name="stage_foundations">The Twin Foundations</string>
    <string name="stage_genai_specialist">The GenAI Specialist</string>
    <string name="stage_ml_practitioner">The ML Practitioner</string>
    <string name="stage_product_visionary">The Product Visionary</string>
    <string name="status_completed">Completed</string>
    <string name="status_in_progress">In Progress</string>
    <string name="status_locked">Locked</string>
    <string name="status_todo">To Do</string>
    <style name="Base.Theme.CodexAIJourneyPlanner" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>

        
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>

        
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
    </style>
    <style name="Theme.CodexAIJourneyPlanner" parent="Base.Theme.CodexAIJourneyPlanner"/>
</resources>