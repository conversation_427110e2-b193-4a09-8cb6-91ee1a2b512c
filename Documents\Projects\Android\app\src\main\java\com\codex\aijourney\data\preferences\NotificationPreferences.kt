package com.codex.aijourney.data.preferences

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationPreferences @Inject constructor(
    private val context: Context
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "notification_preferences",
        Context.MODE_PRIVATE
    )
    
    companion object {
        private const val KEY_LEARNING_REMINDERS_ENABLED = "learning_reminders_enabled"
        private const val KEY_PROGRESS_UPDATES_ENABLED = "progress_updates_enabled"
        private const val KEY_WEEKLY_DIGEST_ENABLED = "weekly_digest_enabled"
        private const val KEY_REMINDER_TIME_HOUR = "reminder_time_hour"
        private const val KEY_REMINDER_TIME_MINUTE = "reminder_time_minute"
        private const val KEY_WEEKLY_DIGEST_DAY = "weekly_digest_day"
        private const val KEY_WEEKLY_DIGEST_HOUR = "weekly_digest_hour"
        private const val KEY_SMART_NUDGES_ENABLED = "smart_nudges_enabled"
        private const val KEY_CELEBRATION_NOTIFICATIONS_ENABLED = "celebration_notifications_enabled"
        private const val KEY_QUIET_HOURS_ENABLED = "quiet_hours_enabled"
        private const val KEY_QUIET_HOURS_START = "quiet_hours_start"
        private const val KEY_QUIET_HOURS_END = "quiet_hours_end"
        private const val KEY_LAST_NOTIFICATION_TIME = "last_notification_time"
        private const val KEY_NOTIFICATION_FREQUENCY = "notification_frequency"
    }
    
    // Learning reminders
    var learningRemindersEnabled: Boolean
        get() = prefs.getBoolean(KEY_LEARNING_REMINDERS_ENABLED, true)
        set(value) = prefs.edit { putBoolean(KEY_LEARNING_REMINDERS_ENABLED, value) }
    
    // Progress updates
    var progressUpdatesEnabled: Boolean
        get() = prefs.getBoolean(KEY_PROGRESS_UPDATES_ENABLED, true)
        set(value) = prefs.edit { putBoolean(KEY_PROGRESS_UPDATES_ENABLED, value) }
    
    // Weekly digest
    var weeklyDigestEnabled: Boolean
        get() = prefs.getBoolean(KEY_WEEKLY_DIGEST_ENABLED, true)
        set(value) = prefs.edit { putBoolean(KEY_WEEKLY_DIGEST_ENABLED, value) }
    
    // Reminder time (default: 7 PM)
    var reminderTimeHour: Int
        get() = prefs.getInt(KEY_REMINDER_TIME_HOUR, 19)
        set(value) = prefs.edit { putInt(KEY_REMINDER_TIME_HOUR, value) }
    
    var reminderTimeMinute: Int
        get() = prefs.getInt(KEY_REMINDER_TIME_MINUTE, 0)
        set(value) = prefs.edit { putInt(KEY_REMINDER_TIME_MINUTE, value) }
    
    // Weekly digest day (default: Sunday = 1)
    var weeklyDigestDay: Int
        get() = prefs.getInt(KEY_WEEKLY_DIGEST_DAY, 1)
        set(value) = prefs.edit { putInt(KEY_WEEKLY_DIGEST_DAY, value) }
    
    // Weekly digest hour (default: 6 PM)
    var weeklyDigestHour: Int
        get() = prefs.getInt(KEY_WEEKLY_DIGEST_HOUR, 18)
        set(value) = prefs.edit { putInt(KEY_WEEKLY_DIGEST_HOUR, value) }
    
    // Smart nudges
    var smartNudgesEnabled: Boolean
        get() = prefs.getBoolean(KEY_SMART_NUDGES_ENABLED, true)
        set(value) = prefs.edit { putBoolean(KEY_SMART_NUDGES_ENABLED, value) }
    
    // Celebration notifications
    var celebrationNotificationsEnabled: Boolean
        get() = prefs.getBoolean(KEY_CELEBRATION_NOTIFICATIONS_ENABLED, true)
        set(value) = prefs.edit { putBoolean(KEY_CELEBRATION_NOTIFICATIONS_ENABLED, value) }
    
    // Quiet hours
    var quietHoursEnabled: Boolean
        get() = prefs.getBoolean(KEY_QUIET_HOURS_ENABLED, false)
        set(value) = prefs.edit { putBoolean(KEY_QUIET_HOURS_ENABLED, value) }
    
    var quietHoursStart: Int
        get() = prefs.getInt(KEY_QUIET_HOURS_START, 22) // 10 PM
        set(value) = prefs.edit { putInt(KEY_QUIET_HOURS_START, value) }
    
    var quietHoursEnd: Int
        get() = prefs.getInt(KEY_QUIET_HOURS_END, 8) // 8 AM
        set(value) = prefs.edit { putInt(KEY_QUIET_HOURS_END, value) }
    
    // Tracking
    var lastNotificationTime: Long
        get() = prefs.getLong(KEY_LAST_NOTIFICATION_TIME, 0)
        set(value) = prefs.edit { putLong(KEY_LAST_NOTIFICATION_TIME, value) }
    
    // Notification frequency (hours between notifications)
    var notificationFrequency: Int
        get() = prefs.getInt(KEY_NOTIFICATION_FREQUENCY, 24)
        set(value) = prefs.edit { putInt(KEY_NOTIFICATION_FREQUENCY, value) }
    
    // Helper methods
    fun isInQuietHours(): Boolean {
        if (!quietHoursEnabled) return false
        
        val currentHour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        
        return if (quietHoursStart <= quietHoursEnd) {
            // Same day quiet hours (e.g., 10 PM to 11 PM)
            currentHour in quietHoursStart..quietHoursEnd
        } else {
            // Overnight quiet hours (e.g., 10 PM to 8 AM)
            currentHour >= quietHoursStart || currentHour <= quietHoursEnd
        }
    }
    
    fun shouldShowNotification(): Boolean {
        if (isInQuietHours()) return false
        
        val timeSinceLastNotification = System.currentTimeMillis() - lastNotificationTime
        val frequencyMillis = notificationFrequency * 60 * 60 * 1000L // Convert hours to milliseconds
        
        return timeSinceLastNotification >= frequencyMillis
    }
    
    fun markNotificationShown() {
        lastNotificationTime = System.currentTimeMillis()
    }
    
    fun getReminderTimeString(): String {
        val hour = if (reminderTimeHour == 0) 12 else if (reminderTimeHour > 12) reminderTimeHour - 12 else reminderTimeHour
        val amPm = if (reminderTimeHour < 12) "AM" else "PM"
        val minute = String.format("%02d", reminderTimeMinute)
        return "$hour:$minute $amPm"
    }
    
    fun getWeeklyDigestTimeString(): String {
        val dayNames = arrayOf("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday")
        val dayName = dayNames.getOrElse(weeklyDigestDay - 1) { "Sunday" }
        val hour = if (weeklyDigestHour == 0) 12 else if (weeklyDigestHour > 12) weeklyDigestHour - 12 else weeklyDigestHour
        val amPm = if (weeklyDigestHour < 12) "AM" else "PM"
        return "$dayName at $hour:00 $amPm"
    }
    
    fun resetToDefaults() {
        prefs.edit {
            clear()
        }
    }
    
    // Notification types enum
    enum class NotificationType {
        LEARNING_REMINDER,
        PROGRESS_UPDATE,
        WEEKLY_DIGEST,
        SMART_NUDGE,
        CELEBRATION
    }
    
    fun isNotificationTypeEnabled(type: NotificationType): Boolean {
        return when (type) {
            NotificationType.LEARNING_REMINDER -> learningRemindersEnabled
            NotificationType.PROGRESS_UPDATE -> progressUpdatesEnabled
            NotificationType.WEEKLY_DIGEST -> weeklyDigestEnabled
            NotificationType.SMART_NUDGE -> smartNudgesEnabled
            NotificationType.CELEBRATION -> celebrationNotificationsEnabled
        }
    }
}
