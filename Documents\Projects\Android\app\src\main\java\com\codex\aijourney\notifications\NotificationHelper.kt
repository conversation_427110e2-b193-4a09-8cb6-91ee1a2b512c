package com.codex.aijourney.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.codex.aijourney.MainActivity
import com.codex.aijourney.R
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationHelper @Inject constructor(
    private val context: Context
) {
    
    companion object {
        const val CHANNEL_ID_LEARNING = "learning_reminders"
        const val CHANNEL_ID_PROGRESS = "progress_updates"
        const val CHANNEL_ID_WEEKLY = "weekly_digest"
        
        const val NOTIFICATION_ID_LEARNING = 1001
        const val NOTIFICATION_ID_PROGRESS = 1002
        const val NOTIFICATION_ID_WEEKLY = 1003
    }
    
    init {
        createNotificationChannels()
    }
    
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_ID_LEARNING,
                    "Learning Reminders",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Reminders about your learning progress and next steps"
                },
                
                NotificationChannel(
                    CHANNEL_ID_PROGRESS,
                    "Progress Updates",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "Updates about your learning milestones and achievements"
                },
                
                NotificationChannel(
                    CHANNEL_ID_WEEKLY,
                    "Weekly Digest",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Weekly summary of your progress and upcoming goals"
                }
            )
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { channel ->
                notificationManager.createNotificationChannel(channel)
            }
        }
    }
    
    fun showLearningReminder(
        title: String,
        message: String,
        nodeId: String? = null
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            nodeId?.let { putExtra("nodeId", it) }
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_LEARNING)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        NotificationManagerCompat.from(context).notify(NOTIFICATION_ID_LEARNING, notification)
    }
    
    fun showProgressUpdate(
        title: String,
        message: String,
        progress: Int? = null
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val builder = NotificationCompat.Builder(context, CHANNEL_ID_PROGRESS)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
        
        progress?.let {
            builder.setProgress(100, it, false)
        }
        
        NotificationManagerCompat.from(context).notify(NOTIFICATION_ID_PROGRESS, builder.build())
    }
    
    fun showWeeklyDigest(
        completedNodes: Int,
        totalNodes: Int,
        currentStage: String,
        nextWeekFocus: String,
        achievements: List<String>
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val progressPercent = if (totalNodes > 0) (completedNodes * 100) / totalNodes else 0
        
        val bigTextContent = buildString {
            appendLine("📊 This Week's Progress:")
            appendLine("• Completed $completedNodes/$totalNodes learning modules")
            appendLine("• Currently in: $currentStage")
            appendLine()
            
            if (achievements.isNotEmpty()) {
                appendLine("🏆 Achievements:")
                achievements.forEach { achievement ->
                    appendLine("• $achievement")
                }
                appendLine()
            }
            
            appendLine("🎯 Next Week's Focus:")
            appendLine(nextWeekFocus)
            appendLine()
            appendLine("Keep up the great work! 🚀")
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_WEEKLY)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("📈 Weekly Learning Digest")
            .setContentText("You completed $completedNodes modules this week! ($progressPercent% total progress)")
            .setStyle(NotificationCompat.BigTextStyle().bigText(bigTextContent))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        NotificationManagerCompat.from(context).notify(NOTIFICATION_ID_WEEKLY, notification)
    }
    
    fun showContextualNudge(
        stage: String,
        message: String,
        actionText: String = "Continue Learning"
    ) {
        val stageEmoji = when (stage.lowercase()) {
            "foundations" -> "🏗️"
            "ml practitioner" -> "🤖"
            "genai specialist" -> "🧠"
            "architect & engineer" -> "⚙️"
            "product visionary" -> "🚀"
            else -> "📚"
        }
        
        showLearningReminder(
            title = "$stageEmoji $stage",
            message = message
        )
    }
    
    fun cancelAllNotifications() {
        NotificationManagerCompat.from(context).cancelAll()
    }
    
    fun cancelNotification(notificationId: Int) {
        NotificationManagerCompat.from(context).cancel(notificationId)
    }
}
