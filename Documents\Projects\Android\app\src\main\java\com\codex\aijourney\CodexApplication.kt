package com.codex.aijourney

import android.app.Application
import com.codex.aijourney.data.initializer.DataInitializer
import com.codex.aijourney.notifications.NotificationScheduler
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class CodexApplication : Application() {

    @Inject
    lateinit var dataInitializer: DataInitializer

    @Inject
    lateinit var notificationScheduler: NotificationScheduler

    override fun onCreate() {
        super.onCreate()
        dataInitializer.initializeAppData()
        notificationScheduler.scheduleAllNotifications()
    }
}
