package com.codex.aijourney

import android.app.Application
import com.codex.aijourney.data.initializer.DataInitializer
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class CodexApplication : Application() {

    @Inject
    lateinit var dataInitializer: DataInitializer

    override fun onCreate() {
        super.onCreate()
        dataInitializer.initializeAppData()
    }
}
