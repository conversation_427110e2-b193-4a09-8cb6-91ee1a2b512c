1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.codex.aijourney"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:6:5-77
13-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:8:22-65
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
15-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:22-76
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
16-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
17-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
18
19    <permission
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.codex.aijourney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.codex.aijourney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:10:5-31:19
26        android:name="com.codex.aijourney.CodexApplication"
26-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:11:9-41
27        android:allowBackup="true"
27-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:12:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\003b9c8fc724b7946ce98db2266de440\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:13:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:14:9-54
33        android:icon="@drawable/ic_launcher"
33-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:15:9-45
34        android:label="@string/app_name"
34-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:16:9-41
35        android:roundIcon="@drawable/ic_launcher"
35-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:17:9-50
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:18:9-35
37        android:theme="@style/Theme.CodexAIJourneyPlanner" >
37-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:19:9-59
38        <activity
38-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:21:9-30:20
39            android:name="com.codex.aijourney.MainActivity"
39-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:22:13-41
40            android:exported="true"
40-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:23:13-36
41            android:label="@string/app_name"
41-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:24:13-45
42            android:theme="@style/Theme.CodexAIJourneyPlanner" >
42-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:25:13-63
43            <intent-filter>
43-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:26:13-29:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:27:17-69
44-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:27:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:28:17-77
46-->C:\Users\<USER>\Documents\Projects\Android\app\src\main\AndroidManifest.xml:28:27-74
47            </intent-filter>
48        </activity>
49
50        <provider
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
51            android:name="androidx.startup.InitializationProvider"
51-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
52            android:authorities="com.codex.aijourney.androidx-startup"
52-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
53            android:exported="false" >
53-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
54            <meta-data
54-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
55                android:name="androidx.work.WorkManagerInitializer"
55-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
56                android:value="androidx.startup" />
56-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
57            <meta-data
57-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ea112f769d7e0745bacd08b3094f624\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\529736ef5ce721926aadaf3201ebe911\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <service
68-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
69            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
69-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
70            android:directBootAware="false"
70-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
71            android:enabled="@bool/enable_system_alarm_service_default"
71-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
72            android:exported="false" />
72-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
73        <service
73-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
74            android:name="androidx.work.impl.background.systemjob.SystemJobService"
74-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
75            android:directBootAware="false"
75-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
76            android:enabled="@bool/enable_system_job_service_default"
76-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
77            android:exported="true"
77-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
78            android:permission="android.permission.BIND_JOB_SERVICE" />
78-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
79        <service
79-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
80            android:name="androidx.work.impl.foreground.SystemForegroundService"
80-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
81            android:directBootAware="false"
81-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
82            android:enabled="@bool/enable_system_foreground_service_default"
82-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
83            android:exported="false" />
83-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
84
85        <receiver
85-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
86            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
86-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
87            android:directBootAware="false"
87-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
88            android:enabled="true"
88-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
89            android:exported="false" />
89-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
90        <receiver
90-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
91            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
91-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
92            android:directBootAware="false"
92-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
93            android:enabled="false"
93-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
94            android:exported="false" >
94-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
95            <intent-filter>
95-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
96                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
96-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
96-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
97                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
97-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
97-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
98            </intent-filter>
99        </receiver>
100        <receiver
100-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
101            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
102            android:directBootAware="false"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
103            android:enabled="false"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
104            android:exported="false" >
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
105            <intent-filter>
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
106                <action android:name="android.intent.action.BATTERY_OKAY" />
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
107                <action android:name="android.intent.action.BATTERY_LOW" />
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
108            </intent-filter>
109        </receiver>
110        <receiver
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
111            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
112            android:directBootAware="false"
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
113            android:enabled="false"
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
114            android:exported="false" >
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
115            <intent-filter>
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
116                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
117                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
118            </intent-filter>
119        </receiver>
120        <receiver
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
121            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
122            android:directBootAware="false"
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
123            android:enabled="false"
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
124            android:exported="false" >
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
125            <intent-filter>
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
126                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
127            </intent-filter>
128        </receiver>
129        <receiver
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
130            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
131            android:directBootAware="false"
131-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
132            android:enabled="false"
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
133            android:exported="false" >
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
134            <intent-filter>
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
135                <action android:name="android.intent.action.BOOT_COMPLETED" />
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
136                <action android:name="android.intent.action.TIME_SET" />
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
137                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
138            </intent-filter>
139        </receiver>
140        <receiver
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
141            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
142            android:directBootAware="false"
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
143            android:enabled="@bool/enable_system_alarm_service_default"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
144            android:exported="false" >
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
145            <intent-filter>
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
146                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
147            </intent-filter>
148        </receiver>
149        <receiver
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
150            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
152            android:enabled="true"
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
153            android:exported="true"
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
154            android:permission="android.permission.DUMP" >
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
155            <intent-filter>
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
156                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eed21004deab6640172f1382b4314917\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
157            </intent-filter>
158        </receiver>
159
160        <activity
160-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
161            android:name="androidx.activity.ComponentActivity"
161-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
162            android:exported="true" />
162-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f8a8eb01502a3e23c26cd0d784ae207\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
163        <activity
163-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
164            android:name="androidx.compose.ui.tooling.PreviewActivity"
164-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
165            android:exported="true" />
165-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7e77306530b588e962044634c510cd0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
166
167        <service
167-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
168            android:name="androidx.room.MultiInstanceInvalidationService"
168-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
169            android:directBootAware="true"
169-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
170            android:exported="false" />
170-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\20fd3962bf0aeda3e7163248cb3f1200\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
171
172        <receiver
172-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
173            android:name="androidx.profileinstaller.ProfileInstallReceiver"
173-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
174            android:directBootAware="false"
174-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
175            android:enabled="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
176            android:exported="true"
176-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
177            android:permission="android.permission.DUMP" >
177-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
178            <intent-filter>
178-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
179                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
179-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
179-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
180            </intent-filter>
181            <intent-filter>
181-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
182                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
182-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
182-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
183            </intent-filter>
184            <intent-filter>
184-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
185                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
186            </intent-filter>
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
188                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa761b8d3342c0c336615f1386c78004\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
189            </intent-filter>
190        </receiver>
191    </application>
192
193</manifest>
