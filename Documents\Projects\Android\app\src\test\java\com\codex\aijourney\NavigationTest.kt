package com.codex.aijourney

import com.codex.aijourney.navigation.Screen
import org.junit.Test
import org.junit.Assert.*

/**
 * Simple unit tests to validate navigation logic
 */
class NavigationTest {
    
    @Test
    fun screen_routes_are_correct() {
        assertEquals("roadmap", Screen.Roadmap.route)
        assertEquals("node_detail/{nodeId}", Screen.NodeDetail.route)
        assertEquals("capstone_hub", Screen.CapstoneHub.route)
    }
    
    @Test
    fun nodeDetail_createRoute_works() {
        val testNodeId = "test_node_123"
        val expectedRoute = "node_detail/test_node_123"
        assertEquals(expectedRoute, Screen.NodeDetail.createRoute(testNodeId))
    }
    
    @Test
    fun nodeDetail_arguments_configured() {
        val arguments = Screen.NodeDetail.arguments
        assertEquals(1, arguments.size)
        assertEquals("nodeId", arguments[0].name)
    }
}
