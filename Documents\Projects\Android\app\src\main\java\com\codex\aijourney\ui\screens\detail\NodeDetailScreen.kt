package com.codex.aijourney.ui.screens.detail

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.codex.aijourney.data.model.NodeStatus
import com.codex.aijourney.data.model.UserNote
import com.codex.aijourney.ui.components.NodeHeaderCard
import com.codex.aijourney.ui.components.NotesSection
import com.codex.aijourney.ui.components.NoteEditDialog
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NodeDetailScreen(
    nodeId: String,
    onBackClick: () -> Unit,
    onCapstoneClick: () -> Unit,
    viewModel: NodeDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current

    // Load node details when screen opens
    LaunchedEffect(nodeId) {
        viewModel.loadNodeDetails(nodeId)
    }

    // Handle errors
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // In a real app, show snackbar
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = uiState.node?.title ?: "Loading...",
                        maxLines = 1
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    // Quick status change button
                    uiState.node?.let { node ->
                        val nextAction = viewModel.getNextStatusAction()
                        nextAction?.let { (status, label) ->
                            TextButton(
                                onClick = { viewModel.updateNodeStatus(status) }
                            ) {
                                Text(label)
                            }
                        }
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { viewModel.startEditingNote() }
            ) {
                Icon(Icons.Default.Add, contentDescription = "Add Note")
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator()
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Loading node details...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.Gray
                            )
                        }
                    }
                }

                uiState.error != null -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "⚠️",
                                style = MaterialTheme.typography.headlineLarge
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Failed to load node",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = uiState.error ?: "Unknown error",
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                color = Color.Gray
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(
                                onClick = { viewModel.loadNodeDetails(nodeId) }
                            ) {
                                Text("Retry")
                            }
                        }
                    }
                }

                uiState.node != null -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // Node header card
                        item {
                            NodeHeaderCard(
                                node = uiState.node!!,
                                statusText = viewModel.getStatusDisplayText(),
                                duration = viewModel.getFormattedDuration(),
                                onResourceClick = { url ->
                                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                                    context.startActivity(intent)
                                },
                                onCapstoneClick = { viewModel.addToCapstoneProject() }
                            )
                        }

                        // Notes section
                        item {
                            NotesSection(
                                notes = uiState.notes,
                                onEditNote = { noteId -> viewModel.startEditingNote(noteId) },
                                onDeleteNote = { noteId -> viewModel.deleteNote(noteId) }
                            )
                        }

                        // Bottom spacing for FAB
                        item {
                            Spacer(modifier = Modifier.height(80.dp))
                        }
                    }
                }
            }
        }
    }

    // Note editing dialog
    if (uiState.isEditingNote) {
        NoteEditDialog(
            noteContent = uiState.noteContent,
            onContentChange = viewModel::updateNoteContent,
            onSave = viewModel::saveNote,
            onCancel = viewModel::cancelEditingNote,
            isEditing = uiState.editingNoteId != null
        )
    }
}
