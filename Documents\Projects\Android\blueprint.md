

# Based on the "Codex: Your AI Journey Planner" blueprint in the project_planner.md file, create a comprehensive system design document and implementation guide for building this Android app. The deliverable should include:

**System Architecture \& Design:**

1. Complete MVVM architecture diagram showing data flow between components
2. Database schema design using Room Persistence Library for storing roadmap progress, notes, and capstone data
3. Navigation flow between screens (Dynamic Roadmap → Detail View → Capstone Hub)
4. Component breakdown for the skill tree UI using Jetpack Compose

**Technical Implementation Guide:**

1. Project structure with all necessary packages and modules
2. Gradle dependencies list for Jetpack Compose, Room, WorkManager, and Navigation
3. Data models/entities for Stages, Nodes, Notes, and Capstone data
4. Repository pattern implementation for data access
5. ViewModel classes for each screen with state management
6. Composable UI components for the roadmap visualization and forms

**Development Roadmap:**

1. Phase-by-phase implementation plan (MVP → Full Features)
2. File-by-file creation order with dependencies
3. Testing strategy for each component

**AI Assistant Instructions:**
Provide detailed prompts and specifications that can be given to an AI coding assistant (like Cursor) to generate each component, including:

- Exact file structures and naming conventions
- Code templates with placeholder comments
- Integration points between components
- Error handling and edge cases to consider

The goal is to have a complete blueprint that enables an AI assistant to build a fully functional Android app matching the described features and user experience

Based on the "Codex: Your AI Journey Planner" blueprint, here is a comprehensive system design document and implementation guide for building the Android application.

## System Architecture \& Design

This section outlines the app's architecture, data storage, navigation, and UI component design, establishing a robust foundation based on modern Android development practices [^9][^18].

### 1. MVVM Architecture Diagram

The application will use the Model-View-ViewModel (MVVM) architecture to separate the UI (View) from the business logic and data [^9][^14][^16]. This promotes a clean, maintainable, and testable codebase.

**Data Flow:**

1. **View (Composable UI):** The UI, built with Jetpack Compose, observes state changes from the ViewModel and sends user events (e.g., button clicks) to it [^9]. It does not contain any business logic.
2. **ViewModel:** It receives user events from the View, processes them, and updates the UI state. It interacts with the Repository to fetch or save data. It exposes data to the View via `StateFlow` [^3]. The ViewModel does not have a direct reference to the View.
3. **Repository:** This class acts as a single source of truth for data [^8]. It abstracts the data sources from the rest of the app, fetching data from the local Room database. It will handle the logic of whether to fetch from a remote source or local cache if the app were to scale [^8][^13].
4. **Model (Data Source):** This layer consists of the Room database, including Entities (data classes) and Data Access Objects (DAOs) for database operations [^4].
```mermaid
graph TD
    subgraph "View Layer"
        View[Composable Screens e.g., RoadmapScreen]
    end

    subgraph "ViewModel Layer"
        RoadmapViewModel
        DetailViewModel
        CapstoneViewModel
    end

    subgraph "Data Layer"
        Repository[CodexRepository]
        subgraph "Local DataSource"
            RoomDB[Room Database: CodexDatabase]
            DAO[DAOs: NodeDao, NoteDao, CapstoneDao]
        end
    end

    View -- "User Events (e.g., tap node)" --> RoadmapViewModel
    RoadmapViewModel -- "Observes State (StateFlow)" --> View
    RoadmapViewModel -- "Fetches/Saves Data" --> Repository
    Repository -- "Accesses Data" --> DAO
    DAO -- "Executes Queries" --> RoomDB
    RoomDB -- "Returns Data" --> DAO
    DAO -- "Returns Data" --> Repository
    Repository -- "Returns Data (Kotlin Flow)" --> RoadmapViewModel
```


### 2. Database Schema Design (Room)

The database will use the Room Persistence Library to store all user-generated data locally [^1][^4].

**Entities:**

* **`Stage`**: Represents a major section of the roadmap (e.g., "The Twin Foundations") [^1].
    * `stageId` (Primary Key, Integer)
    * `title` (String)
    * `order` (Integer)
* **`Node`**: Represents a specific course or task within a Stage [^1].
    * `nodeId` (Primary Key, Integer)
    * `stageId` (Foreign Key to `Stage`, Integer)
    * `title` (String)
    * `description` (String)
    * `status` (String: "Locked", "To-Do", "In Progress", "Completed")
    * `resourceUrl` (String, Nullable)
    * `prerequisites` (List<Integer>, TypeConverter for Node IDs)
* **`Note`**: Personal notes attached to a specific Node [^1].
    * `noteId` (Primary Key, Integer)
    * `nodeId` (Foreign Key to `Node`, Integer)
    * `content` (String)
    * `lastUpdated` (Long, Timestamp)
* **`CapstonePrd`**: The "One-Page PRD" for the capstone project [^1].
    * `id` (Primary Key, fixed to 1)
    * `problem` (String)
    * `user` (String)
    * `mvpFeatures` (String)
* **`DecisionLog`**: A log of key technical and product choices for the capstone [^1].
    * `logId` (Primary Key, Integer)
    * `date` (Long, Timestamp)
    * `decision` (String)
* **`ProjectTask`**: A to-do item for the capstone project [^1].
    * `taskId` (Primary Key, Integer)
    * `description` (String)
    * `isCompleted` (Boolean)


### 3. Navigation Flow

Navigation will be handled using Jetpack Compose Navigation, defined within a single `NavHost` [^5][^7][^21].

1. **Main Screen (`/roadmap`):** The app opens to the `Dynamic Roadmap` screen. This is the start destination.
2. **Detail View (`/detail/{nodeId}`):** Tapping a node on the roadmap navigates to the `Detail View` for that specific node, passing the `nodeId` as an argument [^7].
3. **Capstone Hub (`/capstone`):** Accessible via a bottom navigation bar from the main screen. This destination hosts the PRD, decision logs, and project tasks [^1].
```mermaid
graph LR
    A[Dynamic Roadmap Screen] -->|Tap Node| B(Detail View Screen)
    A -->|Bottom Nav| C(Capstone Hub Screen)
    B -->|Back| A
    C -->|Back| A
```


### 4. Component Breakdown (Skill Tree UI)

The "Dynamic Roadmap" screen will be built with Jetpack Compose composables [^1][^6].

* **`RoadmapScreen.kt`**: The main composable that holds the state, observes the ViewModel, and contains the overall layout, including the bottom navigation bar.
* **`RoadmapList.kt`**: A `LazyColumn` that displays the list of stages and their nodes.
* **`StageHeader.kt`**: A composable that displays the title of a stage (e.g., "Stage 1: The Twin Foundations").
* **`NodeItem.kt`**: The core visual component for a single node.
    * It will take `Node` data as a parameter.
    * Its background color and an optional checkmark icon will change based on the `Node.status`.
    * Handles `onClick` to navigate to the Detail View and `onLongClick` to change status.
* **`NodeConnector.kt`**: A custom drawing composable using `Canvas` to draw the connecting lines between nodes, creating the "skill tree" effect mentioned in the blueprint [^1].


## Technical Implementation Guide

This guide provides the file structure, dependencies, and code skeletons needed for implementation.

### 1. Project Structure

The project will be organized by feature, a common practice for scalable MVVM projects [^6][^10][^17].

```
com.codex.journeyplanner/
├── data/
│   ├── local/
│   │   ├── dao/
│   │   │   ├── NodeDao.kt
│   │   │   ├── NoteDao.kt
│   │   │   └── CapstoneDao.kt
│   │   ├── entity/
│   │   │   ├── Stage.kt
│   │   │   ├── Node.kt
│   │   │   └── ... (other entities)
│   │   ├── CodexDatabase.kt
│   │   └── Converters.kt
│   └── repository/
│       └── CodexRepositoryImpl.kt
├── di/
│   └── AppModule.kt  // For Hilt Dependency Injection
├── domain/
│   ├── model/
│   │   └── // Domain models (if needed for mapping)
│   └── repository/
│       └── CodexRepository.kt // Interface
├── ui/
│   ├── roadmap/
│   │   ├── RoadmapScreen.kt
│   │   └── RoadmapViewModel.kt
│   ├── detail/
│   │   ├── DetailScreen.kt
│   │   └── DetailViewModel.kt
│   ├── capstone/
│   │   ├── CapstoneScreen.kt
│   │   └── CapstoneViewModel.kt
│   ├── components/
│   │   ├── NodeItem.kt
│   │   └── ... (reusable components)
│   └── theme/
│       ├── Color.kt
│       ├── Theme.kt
│       └── Type.kt
├── workers/
│   └── NotificationWorker.kt
└── MainActivity.kt
└── CodexApplication.kt // For Hilt
```


### 2. Gradle Dependencies

Add the following dependencies to your `app/build.gradle.kts` file.

```kotlin
dependencies {
    // Core & UI
    implementation("androidx.core:core-ktx:1.13.1")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.3")
    implementation("androidx.activity:activity-compose:1.9.0")
    implementation(platform("androidx.compose:compose-bom:2024.06.00"))
    implementation("androidx.compose.ui:ui")
    implementation("androidx.compose.ui:ui-graphics")
    implementation("androidx.compose.ui:ui-tooling-preview")
    implementation("androidx.compose.material3:material3")

    // Navigation
    implementation("androidx.navigation:navigation-compose:2.8.0-beta03") [^7]

    // ViewModel
    implementation("androidx.lifecycle:lifecycle-viewmodel-compose:2.8.3")

    // Room Database
    val roomVersion = "2.6.1"
    implementation("androidx.room:room-runtime:$roomVersion")
    annotationProcessor("androidx.room:room-compiler:$roomVersion")
    ksp("androidx.room:room-compiler:$roomVersion") // Use KSP
    implementation("androidx.room:room-ktx:$roomVersion") [^2]

    // WorkManager for background tasks
    val workVersion = "2.9.0"
    implementation("androidx.work:work-runtime-ktx:$workVersion") [^1]

    // Dagger Hilt for Dependency Injection
    implementation("com.google.dagger:hilt-android:2.51.1")
    ksp("com.google.dagger:hilt-compiler:2.51.1")
    implementation("androidx.hilt:hilt-navigation-compose:1.2.0")

    // Testing
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.2.1")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")
    androidTestImplementation(platform("androidx.compose:compose-bom:2024.06.00"))
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    debugImplementation("androidx.compose.ui:ui-tooling")
    debugImplementation("androidx.compose.ui:ui-test-manifest")
}
```


### 3. Data Models/Entities

(See Database Schema section for definitions). Create Kotlin `data class` files for each entity with the appropriate Room annotations (`@Entity`, `@PrimaryKey`, `@ForeignKey`, `@ColumnInfo`).

### 4. Repository Pattern Implementation

The repository will provide a clean API for data access [^8].

**`CodexRepository.kt` (Interface)**

```kotlin
// domain/repository/CodexRepository.kt
interface CodexRepository {
    fun getRoadmap(): Flow<Map<Stage, List<Node>>>
    fun getNodeWithNotes(nodeId: Int): Flow<Pair<Node, List<Note>>>
    suspend fun updateNodeStatus(nodeId: Int, newStatus: String)
    suspend fun saveNote(note: Note)
    // ... other methods for capstone data
}
```

**`CodexRepositoryImpl.kt` (Implementation)**

```kotlin
// data/repository/CodexRepositoryImpl.kt
class CodexRepositoryImpl @Inject constructor(
    private val nodeDao: NodeDao,
    private val noteDao: NoteDao
    // ... other DAOs
) : CodexRepository {
    // Implement interface methods using DAOs
    override fun getRoadmap(): Flow<Map<Stage, List<Node>>> {
        // Logic to fetch stages and their nodes, then map them
    }
    // ...
}
```


### 5. ViewModel Classes

Each screen will have a ViewModel to manage its state and logic [^13][^14].

**`RoadmapViewModel.kt`**

```kotlin
// ui/roadmap/RoadmapViewModel.kt
@HiltViewModel
class RoadmapViewModel @Inject constructor(
    private val repository: CodexRepository
) : ViewModel() {
    private val _uiState = MutableStateFlow<RoadmapState>(RoadmapState.Loading)
    val uiState: StateFlow<RoadmapState> = _uiState

    init {
        viewModelScope.launch {
            repository.getRoadmap().collect { roadmapData ->
                _uiState.value = RoadmapState.Success(roadmapData)
            }
        }
    }

    fun onNodeLongPressed(nodeId: Int, currentStatus: String) {
        viewModelScope.launch {
            // Logic to determine next status and call repository.updateNodeStatus()
        }
    }
}

sealed interface RoadmapState {
    data object Loading : RoadmapState
    data class Success(val roadmap: Map<Stage, List<Node>>) : RoadmapState
    data class Error(val message: String) : RoadmapState
}
```


### 6. Composable UI Components

Define composables for each piece of the UI.

**`RoadmapScreen.kt`**

```kotlin
// ui/roadmap/RoadmapScreen.kt
@Composable
fun RoadmapScreen(
    navController: NavController,
    viewModel: RoadmapViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        bottomBar = { /* Bottom Navigation Composable */ }
    ) { paddingValues ->
        when (val state = uiState) {
            is RoadmapState.Loading -> CircularProgressIndicator()
            is RoadmapState.Success -> RoadmapList(
                roadmap = state.roadmap,
                onNodeClick = { nodeId -> navController.navigate("detail/$nodeId") },
                onNodeLongClick = { nodeId, status -> viewModel.onNodeLongPressed(nodeId, status) }
            )
            is RoadmapState.Error -> Text(text = state.message)
        }
    }
}
```


## Development Roadmap

### 1. Phase-by-Phase Implementation Plan

* **Phase 1: MVP (Core Roadmap \& Notes)**

1. Set up the project, dependencies, and basic theme.
2. Implement all Room entities, DAOs, and the database class [^4].
3. Create the Repository for `Stage`, `Node`, and `Note` data [^8].
4. Build the `RoadmapScreen` with `RoadmapViewModel` to display the static roadmap from a prepopulated database.
5. Implement the `DetailScreen` and `DetailViewModel` to show node details and allow editing/saving notes.
6. Enable navigation between the Roadmap and Detail screens [^7].
7. Implement node status updates via long-press.
* **Phase 2: Capstone Hub**

1. Implement the DAOs and Repository methods for `CapstonePrd`, `DecisionLog`, and `ProjectTask`.
2. Build the `CapstoneScreen` and `CapstoneViewModel` [^1].
3. Create the UI for the "One-Page PRD," Decision Log, and Project Task list.
4. Integrate the Capstone Hub into the bottom navigation bar.
5. Implement the "Add findings to Capstone Hub" button functionality from the `DetailScreen`.
* **Phase 3: Intelligent Features \& Polish**

1. Implement `WorkManager` to schedule weekly digest notifications [^1].
2. Add logic for contextual "nudge" notifications based on user progress.
3. Refine the UI/UX, add animations, and polish the skill tree visuals.
4. Implement a comprehensive testing suite [^11][^20].


### 2. File-by-File Creation Order (for MVP)

1. **Dependencies:** `build.gradle.kts`
2. **Data Layer (Bottom-up):**
    * Entities: `Stage.kt`, `Node.kt`, `Note.kt`
    * DAOs: `NodeDao.kt`, `NoteDao.kt`
    * Database: `CodexDatabase.kt`
    * Repository: `CodexRepository.kt` (interface), `CodexRepositoryImpl.kt`
3. **Dependency Injection:**
    * `CodexApplication.kt`
    * `di/AppModule.kt` (to provide the Database and Repository)
4. **ViewModel Layer:**
    * `ui/roadmap/RoadmapViewModel.kt`
    * `ui/detail/DetailViewModel.kt`
5. **UI Layer (Screens \& Components):**
    * `ui/components/NodeItem.kt`
    * `ui/roadmap/RoadmapScreen.kt`
    * `ui/detail/DetailScreen.kt`
6. **Navigation \& Activity:**
    * `MainActivity.kt` (to set up NavHost and theme)

### 3. Testing Strategy

* **Unit Tests:** Place in `src/test/java`.
    * **Repository:** Test repository methods using a mock/fake DAO. Verify that the correct DAO functions are called.
    * **ViewModel:** Test the ViewModel's logic and state changes without the Android framework. Provide a fake repository to control the data flow and assert that the `uiState` updates correctly based on events [^20].
* **Instrumentation/UI Tests:** Place in `src/androidTest/java`.
    * **DAOs:** Test database queries on an in-memory database to ensure they work as expected [^11].
    * **Composables:** Use `createComposeRule()` to test that UI components render correctly based on different states and that user interactions (clicks, long-presses) trigger the correct callbacks [^11].
    * **Navigation:** Test the full navigation flow between screens.


## AI Assistant Instructions

Here are detailed prompts to generate the application components using an AI coding assistant.

---

**Prompt 1: Create Room Database Entities**

> Create the Kotlin data classes for the "Codex" app using the Room Persistence Library.
>
> 1.  **`Stage.kt`**: An entity named `stages`. It needs `stageId` (Int, PK, auto-generated), `title` (String), and `order` (Int).
> 2.  **`Node.kt`**: An entity named `nodes`. It needs `nodeId` (Int, PK, auto-generated), `stageId` (Int, foreign key to `stages`), `title` (String), `description` (String), `status` (String), `resourceUrl` (String, nullable), and `prerequisites` (String, for a JSON list of node IDs). Add a `@ForeignKey` constraint for `stageId` that references `stages(stageId)`.
> 3.  **`Note.kt`**: An entity named `notes`. It needs `noteId` (Int, PK, auto-generated), `nodeId` (Int, foreign key to `nodes`), `content` (String), and `lastUpdated` (Long). Add a `@ForeignKey` for `nodeId`.
> 4.  **`CapstonePrd.kt`, `DecisionLog.kt`, `ProjectTask.kt`**: Create entities for these based on the schema design document.
>
> Use `@Entity`, `@PrimaryKey(autoGenerate = true)`, `@ColumnInfo`, and `@ForeignKey` annotations correctly.

---

**Prompt 2: Create Room DAOs**

> Create the Data Access Object (DAO) interfaces for the "Codex" app using Room.
>
> 1.  **`NodeDao.kt`**:
>     *   A function `getStagesAndNodes()` that returns a `Flow<Map<Stage, List<Node>>>` using a `@Transaction` and `@Query`. This will require a new data class `StageWithNodes` that has `@Embedded val stage: Stage` and `@Relation val nodes: List<Node>`. The query should be `SELECT * FROM stages ORDER BY 'order' ASC`.
>     *   A function `getNode(nodeId: Int)` that returns a `Flow<Node>`.
>     *   A suspend function `updateNode(node: Node)`.
>     *   A suspend function `insertAllStages(stages: List<Stage>)` and `insertAllNodes(nodes: List<Node>)` for prepopulating data.
> 2.  **`NoteDao.kt`**:
>     *   A function `getNotesForNode(nodeId: Int)` that returns a `Flow<List<Note>>`.
>     *   A suspend function `upsertNote(note: Note)`.
>
> Use `@Dao`, `@Query`, `@Insert(onConflict = OnConflictStrategy.REPLACE)`, `@Update`, and `@Transaction`.

---

**Prompt 3: Create the Room Database and Hilt Module**

> 1.  Create an abstract class `CodexDatabase.kt` that extends `RoomDatabase`. Annotate it with `@Database` and list all the entities (`Stage`, `Node`, `Note`, etc.) and set `version = 1`. It should contain abstract functions for each DAO.
> 2.  Create a Hilt `AppModule.kt` in the `di` package.
>     *   Provide a singleton instance of `CodexDatabase` using `Room.databaseBuilder`.
>     *   Provide singleton instances for each DAO (e.g., `provideNodeDao(database: CodexDatabase)`).
>     *   Provide the `CodexRepository` interface, binding it to `CodexRepositoryImpl`.

---

**Prompt 4: Create RoadmapViewModel**

> Generate a `RoadmapViewModel.kt` for the `RoadmapScreen`.
>
> *   It should be a `@HiltViewModel`.
> *   Inject `CodexRepository`.
> *   Define a sealed interface `RoadmapState` with `Loading`, `Success(val roadmap: Map<Stage, List<Node>>)`, and `Error(val message: String)`.
> *   Use a `MutableStateFlow` for the `_uiState` and expose it as an immutable `StateFlow`.
> *   In an `init` block, collect the flow from `repository.getRoadmap()` and update the `_uiState`. Handle potential errors by catching exceptions and emitting the `Error` state [^15].
> *   Create a public function `updateNodeStatus(nodeId: Int, newStatus: String)` that calls the repository's update method within a `viewModelScope.launch`.

---

**Prompt 5: Create NodeItem Composable**

> Create a Jetpack Compose composable named `NodeItem.kt`.
>
> *   **Parameters**: `node: Node`, `onNodeClick: (Int) -> Unit`, `onNodeLongClick: (Int, String) -> Unit`.
> *   **Layout**: Use a `Card` with elevation. Inside, have a `Row`.
> *   **Left side**: A `Box` with a `Canvas` to draw a circle. The circle's color should depend on `node.status`: Green for "Completed", Blue for "In Progress", Gray for "Locked", White for "To-Do". If "Completed", draw a checkmark icon inside the circle.
> *   **Right side**: A `Column` containing two `Text` composables for `node.title` (bold) and a short part of `node.description`.
> *   **Interaction**: Apply a `combinedClickable` modifier to the Card. The `onClick` should invoke `onNodeClick(node.nodeId)`. The `onLongClick` should invoke `onNodeLongClick(node.nodeId, node.status)`.
> *   Include a `@Preview` composable with sample `Node` data to show all different states.



[^1]: project_planner.md

[^2]: https://saurabhjadhavblogs.com/compose-mvvm-roomdb-with-flow-and-di

[^3]: https://github.com/mori-atsushi/android-flow-mvvm-sample

[^4]: https://www.pluralsight.com/resources/blog/guides/making-a-notes-app-using-room-database

[^5]: https://30dayscoding.com/blog/building-android-apps-with-jetpack-compose-navigation

[^6]: https://jonas-rodehorst.dev/blog/how-to-structure-your-jetpack-compose-project

[^7]: https://www.linkedin.com/pulse/integrating-jetpack-compose-room-workmanager-navigation-soit-rlduf

[^8]: https://developermemos.com/posts/repository-pattern-kotlin/

[^9]: https://getstream.io/blog/design-patterns-and-architecture-the-android-developer-roadmap-part-4/

[^10]: https://stackoverflow.com/questions/49200454/files-structure-in-mvvm-android

[^11]: https://www.youtube.com/watch?v=vNVJBDEB-_M

[^12]: https://getstream.io/blog/android-assistant/

[^13]: https://developer.android.com/codelabs/android-room-with-a-view

[^14]: https://www.digitalocean.com/community/tutorials/android-mvvm-design-pattern

[^15]: https://stackoverflow.com/questions/72349834/in-jetpack-compose-how-do-i-specifically-handle-errors-in-paging3-and-display-t

[^16]: https://github.com/Prashant-Chandel/Jetpack-compose-MVVM-Clean_Architect-Example-with-koin/blob/Developer/README.md

[^17]: https://blog.stackademic.com/mvvm-architecture-and-package-structure-with-jetpack-compose-7158ab583767

[^18]: https://proandroiddev.com/recap-the-android-developer-roadmap-in-2025-84bf11429302

[^19]: https://www.youtube.com/watch?v=MzWX0gyEUiY

[^20]: https://www.youtube.com/watch?v=f2Mvk8BTTy0

[^21]: https://github.com/sajidkhan2067/Android-Credit-Card-List

[^22]: https://developer.android.com/topic/libraries/architecture/viewmodel

[^23]: https://decode.agency/article/android-developer-roadmap-2023/

[^24]: https://www.youtube.com/watch?v=8YPXv7xKh2w

[^25]: https://developer.android.com/topic/architecture

[^26]: https://www.youtube.com/watch?v=TosPS55y_IY

[^27]: https://www.youtube.com/watch?v=9eIhMFTs1Q8

[^28]: https://developer.android.com/develop/ui/compose/architecture

[^29]: https://www.reddit.com/r/androiddev/comments/1g2r49v/best_jetpack_composemvvm_or_clean_app_sample/

[^30]: https://developer.android.com/topic/libraries/architecture/workmanager

[^31]: https://roadmap.sh/pdfs/roadmaps/android.pdf

[^32]: https://dev.to/janirefdez/android-jetpack-compose-mvvm-1nji

[^33]: https://github.com/wicaodian/Jetpack-Compose-News-App-MVVM-Coroutines-Dependency-Injection-Sample

[^34]: https://www.youtube.com/watch?v=0yK7KoruhSM

[^35]: https://www.irjmets.com/uploadedfiles/paper/issue_3_march_2022/20258/final/fin_irjmets1648488221.pdf

[^36]: https://getstream.io/blog/android-developer-roadmap-part-5/

[^37]: https://www.youtube.com/watch?v=FbCA_qQSvYM

